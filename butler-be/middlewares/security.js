import rateLimit from 'express-rate-limit';
import helmet from 'helmet';
import mongoSanitize from 'express-mongo-sanitize';
import { body, validationResult } from 'express-validator';
import compression from 'compression';

// Rate limiting configurations
export const createRateLimit = (windowMs = 15 * 60 * 1000, max = 100, message = 'Too many requests') => {
  return rateLimit({
    windowMs,
    max,
    message: {
      success: false,
      message,
      retryAfter: Math.ceil(windowMs / 1000)
    },
    standardHeaders: true,
    legacyHeaders: false,
    // Skip successful requests
    skipSuccessfulRequests: false,
    // Skip failed requests
    skipFailedRequests: false,
    // Use default key generator to avoid IPv6 issues
    // keyGenerator: default (uses req.ip)
  });
};

// Different rate limits for different endpoints
export const authRateLimit = createRateLimit(
  15 * 60 * 1000, // 15 minutes
  5, // 5 attempts
  'Too many authentication attempts, please try again later'
);

export const apiRateLimit = createRateLimit(
  15 * 60 * 1000, // 15 minutes
  250, // 100 requests
  'Too many API requests, please try again later'
);

export const strictRateLimit = createRateLimit(
  15 * 60 * 1000, // 15 minutes
  10, // 10 requests
  'Rate limit exceeded for this endpoint'
);

export const paymentRateLimit = createRateLimit(
  60 * 60 * 1000, // 1 hour
  20, // 20 payment attempts
  'Too many payment attempts, please try again later'
);

// Security headers configuration
export const securityHeaders = helmet({
  contentSecurityPolicy: {
    directives: {
      defaultSrc: ["'self'"],
      styleSrc: ["'self'", "'unsafe-inline'", "https://fonts.googleapis.com"],
      fontSrc: ["'self'", "https://fonts.gstatic.com"],
      imgSrc: ["'self'", "data:", "https:"],
      scriptSrc: ["'self'"],
      connectSrc: ["'self'", "https://api.groq.com", "https://api.razorpay.com"],
      frameSrc: ["'none'"],
      objectSrc: ["'none'"],
      upgradeInsecureRequests: [],
    },
  },
  crossOriginEmbedderPolicy: false, // Disable for API compatibility
  hsts: {
    maxAge: 31536000,
    includeSubDomains: true,
    preload: true
  }
});

// MongoDB injection protection
export const mongoSanitization = mongoSanitize({
  replaceWith: '_',
  onSanitize: ({ req, key }) => {
    console.warn(`Potential NoSQL injection attempt detected: ${key} from IP: ${req.ip}`);
  }
});

// XSS Protection (manual implementation since xss-clean is deprecated)
export const xssProtection = (req, res, next) => {
  const sanitizeValue = (value) => {
    if (typeof value === 'string') {
      return value
        .replace(/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi, '')
        .replace(/<iframe\b[^<]*(?:(?!<\/iframe>)<[^<]*)*<\/iframe>/gi, '')
        .replace(/javascript:/gi, '')
        .replace(/on\w+\s*=/gi, '')
        .replace(/<[^>]*>/g, '');
    }
    return value;
  };

  const sanitizeObject = (obj) => {
    if (obj && typeof obj === 'object') {
      for (const key in obj) {
        if (obj.hasOwnProperty(key)) {
          if (typeof obj[key] === 'object') {
            sanitizeObject(obj[key]);
          } else {
            obj[key] = sanitizeValue(obj[key]);
          }
        }
      }
    }
  };

  // Sanitize request body
  if (req.body) {
    sanitizeObject(req.body);
  }

  // Sanitize query parameters
  if (req.query) {
    sanitizeObject(req.query);
  }

  // Sanitize URL parameters
  if (req.params) {
    sanitizeObject(req.params);
  }

  next();
};

// Input validation helpers
export const validateEmail = body('email')
  .isEmail()
  .normalizeEmail()
  .withMessage('Please provide a valid email address');

export const validatePassword = body('password')
  .isLength({ min: 8 })
  .withMessage('Password must be at least 8 characters long')
  .matches(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]/)
  .withMessage('Password must contain at least one uppercase letter, one lowercase letter, one number, and one special character');

export const validatePhone = body('phone')
  .isMobilePhone()
  .withMessage('Please provide a valid phone number');

export const validateObjectId = (field) => 
  body(field)
    .isMongoId()
    .withMessage(`${field} must be a valid ID`);

// Validation error handler
export const handleValidationErrors = (req, res, next) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    return res.status(400).json({
      success: false,
      message: 'Validation failed',
      errors: errors.array().map(error => ({
        field: error.param,
        message: error.msg,
        value: error.value
      }))
    });
  }
  next();
};

// Enhanced request logging middleware
export const requestLogger = (req, res, next) => {
  const start = Date.now();

  // Import logger dynamically to avoid circular dependencies
  import('../utils/logger.js').then(({ logHttp, logSecurity, logError }) => {
    res.on('finish', () => {
      const duration = Date.now() - start;

      // Log HTTP request
      logHttp(req, res, duration);

      // Log security events for suspicious activity
      if (res.statusCode === 401 || res.statusCode === 403) {
        logSecurity('unauthorized_access_attempt', {
          method: req.method,
          url: req.originalUrl,
          ip: req.ip,
          userAgent: req.get('User-Agent'),
          statusCode: res.statusCode
        });
      }

      // Log errors for 5xx status codes
      if (res.statusCode >= 500) {
        logError(new Error(`Server error: ${res.statusCode}`), {
          method: req.method,
          url: req.originalUrl,
          ip: req.ip,
          userAgent: req.get('User-Agent'),
          statusCode: res.statusCode,
          duration
        });
      }
    });
  }).catch(console.error);

  next();
};

// Response compression
export const responseCompression = compression({
  filter: (req, res) => {
    if (req.headers['x-no-compression']) {
      return false;
    }
    return compression.filter(req, res);
  },
  threshold: 1024 // Only compress responses larger than 1KB
});

// CORS configuration
export const corsOptions = {
  origin: function (origin, callback) {
    const allowedOrigins = [
      'http://localhost:3000',
      'http://localhost:3001',
      'http://localhost:3002',
      'http://localhost:3003',
      'http://localhost:3004',
      'http://localhost:3005',
      'https://butler-web.vercel.app',
      'https://butler-web-git-main-vaidikchouhans-projects.vercel.app',
      'https://butler-web-vaidikchouhans-projects.vercel.app'
    ];

    // Allow requests with no origin (mobile apps, curl, etc.)
    if (!origin) return callback(null, true);

    if (allowedOrigins.includes(origin)) {
      return callback(null, true);
    } else {
      console.warn(`CORS blocked request from origin: ${origin}`);
      return callback(new Error('Not allowed by CORS'), false);
    }
  },
  credentials: true,
  methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
  allowedHeaders: ['Authorization', 'Content-Type', 'X-Requested-With'],
  exposedHeaders: ['Authorization', 'Content-Type'],
  maxAge: 86400 // 24 hours
};

// Security audit middleware
export const securityAudit = (req, res, next) => {
  // Check for suspicious patterns
  const suspiciousPatterns = [
    /(\<|\%3C)script(\>|\%3E)/i,
    /javascript:/i,
    /vbscript:/i,
    /onload=/i,
    /onerror=/i,
    /eval\(/i,
    /union.*select/i,
    /drop.*table/i
  ];

  const checkValue = (value) => {
    if (typeof value === 'string') {
      return suspiciousPatterns.some(pattern => pattern.test(value));
    }
    return false;
  };

  const checkObject = (obj, path = '') => {
    for (const key in obj) {
      if (obj.hasOwnProperty(key)) {
        const currentPath = path ? `${path}.${key}` : key;
        if (typeof obj[key] === 'object' && obj[key] !== null) {
          if (checkObject(obj[key], currentPath)) return true;
        } else if (checkValue(obj[key])) {
          console.warn(`Suspicious input detected at ${currentPath}: ${obj[key]} from IP: ${req.ip}`);
          return true;
        }
      }
    }
    return false;
  };

  // Check request body, query, and params
  if (req.body && checkObject(req.body, 'body')) {
    return res.status(400).json({
      success: false,
      message: 'Suspicious input detected'
    });
  }

  if (req.query && checkObject(req.query, 'query')) {
    return res.status(400).json({
      success: false,
      message: 'Suspicious input detected'
    });
  }

  if (req.params && checkObject(req.params, 'params')) {
    return res.status(400).json({
      success: false,
      message: 'Suspicious input detected'
    });
  }

  next();
};

export default {
  createRateLimit,
  authRateLimit,
  apiRateLimit,
  strictRateLimit,
  paymentRateLimit,
  securityHeaders,
  mongoSanitization,
  xssProtection,
  validateEmail,
  validatePassword,
  validatePhone,
  validateObjectId,
  handleValidationErrors,
  requestLogger,
  responseCompression,
  corsOptions,
  securityAudit
};
