import {
  smartPreFilter,
  enhancedSemanticSearch,
  optimizedAIProcessing,
  getCacheStats,
  clearRecommendationCache,
} from "./optimized-recommendation-service.js";
import { getRecommendations as getMenuSearchRecommendations } from "./menu-search-service.js";
import { getPersonalizedRecommendations } from "./personalization-service.js";
import {
  shouldUseOptimizedSystem,
  recordABTestMetrics,
} from "./ab-testing-service.js";
import {
  getAtlasVectorRecommendations,
  isAtlasVectorSearchAvailable,
  hybridDishSearch,
} from "./atlas-vector-search-service.js";
import { getVectorFilteredDishesForAI } from "./vector-service.js";
import {
  enhancedSemanticRAGSearch,
  hybridRAGSearch,
  aiPoweredMenuFiltering,
} from "./enhanced-rag-service.js";

/**
 * Check if user is requesting vegetarian options
 */
const checkVegetarianRequest = (userMessage) => {
  const lowerMessage = userMessage.toLowerCase();
  const vegPatterns = [
    "veg option",
    "veg food",
    "vegetarian",
    "veg ",
    "veggie",
    "plant-based",
    "no meat",
    "no chicken",
    "no fish",
    "only veg",
    "pure veg",
    "veg only",
    "show me veg",
    "tell me about veg",
    "veg burger",
    "veg pizza",
    "veg dishes",
    "veg items",
    "veg menu",
    "vegetarian options",
    "vegetarian dishes",
    "vegetarian menu",
  ];

  const nonVegPatterns = [
    "non-veg",
    "non vegetarian",
    "non veg",
    "show me non-veg",
    "tell me about non-veg",
    "non-veg burger",
    "non-veg pizza",
    "non-veg dishes",
    "non-veg items",
    "non-veg menu",
    "non-vegetarian options",
    "non-vegetarian dishes",
    "non-vegetarian menu",
  ];

  const hasVegRequest = vegPatterns.some((pattern) =>
    lowerMessage.includes(pattern)
  );
  const hasNonVegRequest = nonVegPatterns.some((pattern) =>
    lowerMessage.includes(pattern)
  );

  return hasVegRequest && !hasNonVegRequest;
};

/**
 * Check if user is requesting non-vegetarian options
 */
const checkNonVegetarianRequest = (userMessage) => {
  const lowerMessage = userMessage.toLowerCase();
  const nonVegPatterns = [
    "non-veg",
    "non vegetarian",
    "meat",
    "chicken",
    "fish",
    "mutton",
    "beef",
    "pork",
    "egg",
    "non veg",
    "show me non-veg",
    "tell me about non-veg",
    "non-veg burger",
    "non-veg pizza",
    "non-veg dishes",
    "non-veg items",
    "non-veg menu",
    "non-vegetarian options",
    "non-vegetarian dishes",
    "non-vegetarian menu",
  ];

  const vegPatterns = [
    "veg option",
    "veg food",
    "vegetarian",
    "veg ",
    "veggie",
    "show me veg",
    "tell me about veg",
    "veg burger",
    "veg pizza",
    "veg dishes",
    "veg items",
    "veg menu",
    "vegetarian options",
    "vegetarian dishes",
    "vegetarian menu",
  ];

  const hasNonVegRequest = nonVegPatterns.some((pattern) =>
    lowerMessage.includes(pattern)
  );
  const hasVegRequest = vegPatterns.some((pattern) =>
    lowerMessage.includes(pattern)
  );

  return hasNonVegRequest && !hasVegRequest;
};

/**
 * Quick intent detection for fast negative/availability checks
 */
const detectQuickIntent = (userMessage) => {
  const msg = userMessage.toLowerCase();
  const intents = {
    wantsSweet: /(sweet|dessert|mithai|meetha|meetha|sweet dish|ice cream|cake|brownie)/i.test(
      userMessage
    ),
    wantsVeg: checkVegetarianRequest(userMessage),
    wantsNonVeg: checkNonVegetarianRequest(userMessage),
  };
  return intents;
};

/**
 * Fast path: if user asks for something the menu clearly doesn't have,
 * avoid expensive vector/RAG/LLM work and respond immediately.
 */
const fastNoMatchCheck = (userMessage, availableDishes) => {
  const intents = detectQuickIntent(userMessage);
  const dishes = availableDishes || [];

  // Check desserts/sweets presence quickly
  if (intents.wantsSweet) {
    const sweetKeywords = [
      "dessert",
      "sweet",
      "mithai",
      "ice cream",
      "brownie",
      "gulab",
      "jamun",
      "kheer",
      "halwa",
      "rasgulla",
      "laddu",
      "jalebi",
      "cake",
      "pastry",
    ];

    const sweetDishes = dishes.filter((d) => {
      const name = (d.name || "").toLowerCase();
      const desc = (d.description || "").toLowerCase();
      const cat = (d.category?.name || "").toLowerCase();
      const tags = (d.tags || []).map((t) => t.toLowerCase());
      const text = `${name} ${desc} ${cat} ${tags.join(" ")}`;
      return (
        cat.includes("dessert") ||
        tags.includes("dessert") ||
        sweetKeywords.some((k) => text.includes(k))
      );
    });

    if (sweetDishes.length === 0) {
      // Suggest nearest alternatives (sweet beverages) if any
      const altKeywords = ["shake", "lassi", "falooda", "cold coffee", "smoothie"];
      const altDishes = dishes
        .filter((d) => {
          const text = `${(d.name || "").toLowerCase()} ${(d.description || "").toLowerCase()} ${(d.category?.name || "").toLowerCase()} ${(d.tags || []).map((t) => t.toLowerCase()).join(" ")}`;
          return altKeywords.some((k) => text.includes(k));
        })
        .slice(0, 3);

      return {
        isNoMatch: true,
        message:
          "I'm sorry, we don't have any desserts or sweet dishes right now. " +
          (altDishes.length > 0
            ? `Would you like to try ${altDishes
                .map((d) => d.name)
                .join(", ")}?`
            : "Can I suggest something else from the menu?"),
        suggestions: altDishes,
      };
    }
  }

  // If user explicitly wants only veg but menu has zero veg items (rare)
  if (intents.wantsVeg) {
    const vegCount = dishes.filter((d) => d.isVeg === true).length;
    if (vegCount === 0) {
      return {
        isNoMatch: true,
        message:
          "Apologies, there are no vegetarian items available at the moment. Would you like non-veg or beverages instead?",
        suggestions: dishes
          .filter((d) => d.category?.name?.toLowerCase().includes("beverage"))
          .slice(0, 3),
      };
    }
  }

  return { isNoMatch: false };
};


/**
 * Recommendation Orchestrator
 * Main service that coordinates the 3-stage optimization process
 * Provides fallback mechanisms and performance monitoring
 */

// Performance metrics tracking
let performanceMetrics = {
  totalRequests: 0,
  tokensSaved: 0,
  averageResponseTime: 0,
  cacheHitRate: 0,
  fallbackUsage: 0,
  lastReset: new Date(),
};

/**
 * Main recommendation function - replaces the current system
 * @param {string} userMessage - User's query
 * @param {Array} availableDishes - All available dishes
 * @param {string} userId - User ID
 * @param {Object} context - Additional context
 * @returns {Promise<Object>} - Optimized recommendations
 */
export const getOptimizedRecommendations = async (
  userMessage,
  availableDishes,
  userId,
  context = {}
) => {
  const startTime = Date.now();
  performanceMetrics.totalRequests++;

  // Quick fast-path check for obvious no-match cases (e.g., no desserts)
  const quickCheck = fastNoMatchCheck(userMessage, availableDishes);
  if (quickCheck.isNoMatch) {
    const responseTime = Date.now() - startTime;
    const altRecs = (quickCheck.suggestions || []).slice(0, 3);
    return {
      recommendations: altRecs,
      aiResponse: {
        keywords: [],
        aiMessage: quickCheck.message,
        recommendedDishIds: altRecs.map((d) => d._id),
        faqSuggestions: ["Show veg options", "Show beverages"],
        detectedLanguage: context.language || "en",
      },
      performance: {
        responseTime,
        stagesCompleted: 0,
        dishesProcessed: {
          original: availableDishes.length,
          afterPreFilter: 0,
          afterSemantic: 0,
          sentToAI: 0,
        },
        tokenOptimization: {
          estimatedTokens: 0,
          reductionFromFullMenu: "100%",
          dishesReduction: `${availableDishes.length} → 0`,
          menuSummarySize: 0,
          tokenSavings: "~100%",
        },
        ragPerformance: { searchMethod: "fast_path" },
        cacheUsage: { preFilterCached: false, semanticCached: false, aiCached: false },
      },
      fallbackUsed: false,
      optimizationVersion: "1.0",
      abTestVariant: "treatment",
      abTestUsed: true,
    };
  }

  // A/B Testing: Determine which system to use
  const useOptimizedSystem = shouldUseOptimizedSystem(userId, context);
  const variant = useOptimizedSystem ? "treatment" : "control";

  console.log(`🧪 A/B Test: User ${userId} assigned to ${variant} group`);

  try {
    if (!useOptimizedSystem) {
      // Use fallback system for control group
      console.log(`🔄 Using control system (fallback) for user ${userId}`);
      const fallbackResult = await getMenuSearchRecommendations(
        userMessage,
        availableDishes,
        {
          userId,
          ...context,
          fallbackReason: "A/B test control group",
        }
      );

      // Record A/B test metrics for control group
      recordABTestMetrics("control", {
        responseTime: Date.now() - startTime,
        tokenUsage: 0, // Fallback doesn't use tokens
        successful: fallbackResult.recommendations?.length > 0,
        cacheHits: 0,
        cacheMisses: 0,
        fallbackUsed: true,
      });

      return {
        ...fallbackResult,
        abTestVariant: "control",
        abTestUsed: true,
      };
    }

    console.log(`🚀 Starting optimized recommendation for user ${userId}`);
    console.log(`📊 Processing ${availableDishes.length} available dishes`);

    // Stage 0: Atlas Vector Search Pre-filtering (if available)
    let vectorFilteredDishes = availableDishes;
    const atlasAvailable = await isAtlasVectorSearchAvailable();

    if (atlasAvailable && context.foodChainId) {
      console.log("🎯 Stage 0: Atlas Vector Search Pre-filtering...");
      try {
        const atlasResults = await getAtlasVectorRecommendations(
          userMessage,
          availableDishes,
          {
            ...context,
            limit: Math.min(availableDishes.length * 0.3, 50), // Use 30% of dishes or max 50
          }
        );

        if (atlasResults.length > 0) {
          vectorFilteredDishes = atlasResults;
          console.log(
            `✅ Atlas Vector Search: ${vectorFilteredDishes.length} highly relevant dishes selected`
          );
          console.log(
            `📉 Vector pre-filtering reduced dishes by ${Math.round(
              ((availableDishes.length - vectorFilteredDishes.length) /
                availableDishes.length) *
                100
            )}%`
          );
        } else {
          console.log(
            "📭 Atlas Vector Search returned no results, using all dishes"
          );
        }
      } catch (error) {
        console.warn(
          "⚠️ Atlas Vector Search failed, continuing with all dishes:",
          error.message
        );
      }
    } else {
      console.log(
        "⏭️ Skipping Atlas Vector Search (not available or missing foodChainId)"
      );
    }

    // Enhanced Vector Filtering for AI if Atlas Vector Search didn't provide results
    if (vectorFilteredDishes.length === 0) {
      console.log("🎯 Enhanced Vector Filtering for AI optimization...");
      try {
        const vectorResult = await getVectorFilteredDishesForAI(
          userMessage,
          availableDishes,
          {
            maxDishes: 25, // More dishes for next stage processing
            minSimilarity: 0.2, // Lower threshold for broader matching
            includePopular: true,
            userPreferences: context.userPreferences || {},
            contextFilters: {
              isVegetarian: checkVegetarianRequest(userMessage),
              isNonVegetarian: checkNonVegetarianRequest(userMessage),
            },
          }
        );
        vectorFilteredDishes = vectorResult.dishes;
        console.log(
          `✅ Enhanced vector filtering: ${vectorFilteredDishes.length} highly relevant dishes selected`
        );
        console.log(
          `📊 Average similarity: ${
            vectorResult.metadata.averageSimilarity?.toFixed(3) || "N/A"
          }`
        );
        console.log(
          `📉 Reduction: ${vectorResult.metadata.reductionPercentage}%`
        );
      } catch (error) {
        console.error("Error in enhanced vector filtering:", error);
        vectorFilteredDishes = availableDishes.slice(0, 25); // Fallback
      }
    }

    // Stage 1: Smart Pre-filtering (No AI)
    console.log("🔍 Stage 1: Smart Pre-filtering...");
    const preFilterResult = await smartPreFilter(
      userMessage,
      vectorFilteredDishes, // Use vector-filtered dishes instead of all dishes
      userId,
      context
    );

    console.log(
      `✅ Pre-filtering complete: ${preFilterResult.dishes.length} dishes selected`
    );
    console.log(
      `📉 Reduced by ${preFilterResult.filterStats.reductionPercentage}%`
    );

    // Stage 2: Enhanced RAG-based Semantic Search
    console.log("🔎 Stage 2: Enhanced RAG Semantic Search...");
    const wantsVeg = checkVegetarianRequest(userMessage);
    const wantsNonVeg = checkNonVegetarianRequest(userMessage);

    const semanticResult = await hybridRAGSearch(
      userMessage,
      preFilterResult.dishes,
      {
        maxResults: 15,
        minSimilarity: 0.2,
        contextFilters: {
          // Enforce dietary intent from the current message strictly
          isVeg: wantsVeg
            ? true
            : wantsNonVeg
            ? false
            : typeof context.userPreferences?.isVeg === "boolean"
            ? context.userPreferences.isVeg
            : undefined,
          priceRange: context.userPreferences?.priceRange,
          cuisine: context.userPreferences?.preferredCuisine,
          spiceLevel: context.userPreferences?.spiceLevel,
        },
        cacheKey: `rag_search_${context.outletId}_${userMessage.substring(
          0,
          50
        )}`,
        outletId: context.outletId,
      }
    );

    // Strict post-filter to prevent veg/non-veg leakage
    let semanticDishes = semanticResult.dishes;
    let preFilteredDishes = preFilterResult.dishes;
    if (wantsVeg) {
      semanticDishes = semanticDishes.filter((d) => d.isVeg === true);
      preFilteredDishes = preFilteredDishes.filter((d) => d.isVeg === true);
    } else if (wantsNonVeg) {
      semanticDishes = semanticDishes.filter((d) => d.isVeg === false);
      preFilteredDishes = preFilteredDishes.filter((d) => d.isVeg === false);
    }

    console.log(
      `✅ Enhanced RAG search complete: ${semanticResult.dishes.length} relevant dishes found`
    );
    console.log(
      `📊 RAG Performance: ${semanticResult.searchMetadata?.searchMethod} method used`
    );

    // Stage 3: Optimized AI Processing
    console.log("🤖 Stage 3: AI Processing...");
    const aiResult = await optimizedAIProcessing(
      userMessage,
      semanticResult.dishes,
      {
        ...context,
        lastConversation: context.lastConversation || [],
        cartHistory: context.cartHistory || [],
      }
    );

    console.log(`✅ AI processing complete`);
    console.log(
      `💰 Estimated token usage: ${aiResult.tokenUsage.estimatedTokens}`
    );
    console.log(
      `📊 Menu reduction: ${aiResult.tokenUsage.reductionFromFullMenu}`
    );

    // Map AI recommended dish IDs to actual dishes
    const maxCount = aiResult.queryAnalysis?.requestedCount || 3;
    const hasExplicit = Array.isArray(aiResult.aiResponse?.recommendedDishIds) && aiResult.aiResponse.recommendedDishIds.length > 0;
    const recommendedDishes = mapRecommendedDishes(
      aiResult.aiResponse.recommendedDishIds,
      semanticDishes,
      preFilteredDishes,
      maxCount,
      // If AI gave explicit dish IDs, do NOT add extras
      hasExplicit ? false : true
    );

  // SYNC CHECK: If AI recommended dishes but none were found, update AI message to match actual recommendations
  if (hasExplicit && recommendedDishes.length === 0 && semanticDishes.length > 0) {
    console.log("⚠️ AI recommended dishes not found in semantic results, syncing with available dishes");
    const fallbackDishes = semanticDishes.slice(0, maxCount);
    aiResult.aiResponse.recommendedDishIds = fallbackDishes.map(d => d._id.toString());

    // Update AI message to match the actual dishes being shown
    const dishNames = fallbackDishes.map(d => d.name).slice(0, 2).join(" and ");
    const language = context.language || "en";
    if (language === "hi") {
      aiResult.aiResponse.aiMessage = `आपके लिए ${dishNames} जैसे बेहतरीन विकल्प हैं जो आपकी पसंद के अनुकूल हो सकते हैं।`;
    } else {
      aiResult.aiResponse.aiMessage = `I recommend ${dishNames} and other excellent options that match your preferences.`;
    }

    return {
      recommendations: fallbackDishes,
      aiResponse: aiResult.aiResponse,
      performance: {
        responseTime,
        stagesCompleted: 3,
        syncFixed: true,
        dishesProcessed: {
          original: availableDishes.length,
          preFiltered: preFilterResult.dishes.length,
          semantic: semanticResult.dishes.length,
          final: fallbackDishes.length,
        },
        tokenOptimization: aiResult.tokenUsage,
        cacheUsage: {
          preFilter: preFilterResult.fromCache || false,
          semantic: semanticResult.fromCache || false,
          ai: aiResult.fromCache || false,
        },
      },
      abTestVariant: useOptimizedSystem ? "optimized" : "control",
      abTestUsed: true,
      semanticResult
    };
  }

    // Final dietary safety check to prevent leakage
    const wantsVegFinal = checkVegetarianRequest(userMessage);
    const wantsNonVegFinal = checkNonVegetarianRequest(userMessage);
    const finalRecommendations = recommendedDishes.filter((d) =>
      wantsVegFinal ? d.isVeg === true : wantsNonVegFinal ? d.isVeg === false : true
    );

    // Calculate performance metrics
    const responseTime = Date.now() - startTime;
    updatePerformanceMetrics(
      responseTime,
      aiResult,
      preFilterResult,
      semanticResult
    );

    const result = {
      recommendations: finalRecommendations,
      aiResponse: aiResult.aiResponse,
      performance: {
        responseTime,
        stagesCompleted: 3,
        dishesProcessed: {
          original: availableDishes.length,
          afterPreFilter: preFilterResult.dishes.length,
          afterSemantic: semanticDishes.length,
          sentToAI: semanticDishes.length,
        },
        tokenOptimization: {
          estimatedTokens: aiResult.tokenUsage.estimatedTokens,
          reductionFromFullMenu: aiResult.tokenUsage.reductionFromFullMenu,
          dishesReduction: `${availableDishes.length} → ${semanticDishes.length}`,
          menuSummarySize: aiResult.tokenUsage.menuSummarySize,
          tokenSavings: aiResult.tokenUsage.tokenSavings,
        },
        ragPerformance: {
          searchMethod: semanticResult.searchMetadata?.searchMethod,
          totalProcessed: semanticResult.searchMetadata?.totalProcessed,
          ragResults: semanticResult.searchMetadata?.ragResults,
          aiFiltered: semanticResult.searchMetadata?.aiFiltered,
          averageScore: semanticResult.searchMetadata?.averageScore,
        },
        cacheUsage: {
          preFilterCached: preFilterResult.fromCache || false,
          semanticCached: semanticResult.fromCache || false,
          aiCached: aiResult.fromCache || false,
        },
      },
      fallbackUsed: false,
      optimizationVersion: "1.0",
      abTestVariant: "treatment",
      abTestUsed: true,
    };

    // Record A/B test metrics for treatment group
    recordABTestMetrics("treatment", {
      responseTime,
      tokenUsage: aiResult.tokenUsage.estimatedTokens,
      successful: recommendedDishes.length > 0,
      cacheHits: Object.values(result.performance.cacheUsage).filter(Boolean)
        .length,
      cacheMisses: Object.values(result.performance.cacheUsage).filter(
        (hit) => !hit
      ).length,
      fallbackUsed: false,
    });

    console.log(`🎉 Optimization complete in ${responseTime}ms`);
    return result;
  } catch (error) {
    console.error("❌ Error in optimized recommendations:", error);

    // Fallback to existing system
    console.log("🔄 Falling back to existing recommendation system...");
    performanceMetrics.fallbackUsage++;

    return await getFallbackRecommendations(userMessage, availableDishes, {
      userId,
      ...context,
      fallbackReason: error.message,
      responseTime: Date.now() - startTime,
    });
  }
};

/**
 * Fallback recommendation system
 * Uses existing services when optimization fails
 */
const getFallbackRecommendations = async (
  userMessage,
  availableDishes,
  context
) => {
  try {
    // Use existing personalization service as fallback
    const personalizedRecs = await getPersonalizedRecommendations(
      context.userId,
      availableDishes,
      userMessage,
      context.outletId
    );

    if (personalizedRecs && personalizedRecs.length > 0) {
      return {
        recommendations: personalizedRecs.slice(0, 5),
        aiResponse: {
          keywords: [],
          aiMessage: "Here are some personalized recommendations for you.",
          recommendedDishIds: personalizedRecs
            .slice(0, 5)
            .map((dish) => dish._id),
          faqSuggestions: [
            "What's popular today?",
            "Show me vegetarian options",
          ],
          detectedLanguage: context.language || "en",
        },
        performance: {
          responseTime: context.responseTime || 0,
          stagesCompleted: 0,
          fallbackMethod: "personalization",
        },
        fallbackUsed: true,
        fallbackReason: context.fallbackReason,
      };
    }

    // Final fallback - popular dishes
    const popularDishes = availableDishes
      .filter((dish) => dish.isAvailable)
      .sort((a, b) => (b.ratings?.average || 0) - (a.ratings?.average || 0))
      .slice(0, 5);

    return {
      recommendations: popularDishes,
      aiResponse: {
        keywords: [],
        aiMessage: "Here are our popular dishes you might enjoy.",
        recommendedDishIds: popularDishes.map((dish) => dish._id),
        faqSuggestions: ["What's your specialty?", "Show me today's specials"],
        detectedLanguage: context.language || "en",
      },
      performance: {
        responseTime: context.responseTime || 0,
        stagesCompleted: 0,
        fallbackMethod: "popular",
      },
      fallbackUsed: true,
      fallbackReason: context.fallbackReason,
    };
  } catch (fallbackError) {
    console.error("❌ Fallback system also failed:", fallbackError);

    // Ultimate fallback - first 5 available dishes
    const basicDishes = availableDishes
      .filter((dish) => dish.isAvailable)
      .slice(0, 5);

    return {
      recommendations: basicDishes,
      aiResponse: {
        keywords: [],
        aiMessage: "I can help you explore our menu.",
        recommendedDishIds: basicDishes.map((dish) => dish._id),
        faqSuggestions: ["What do you recommend?", "Show me the menu"],
        detectedLanguage: "en",
      },
      performance: {
        responseTime: context.responseTime || 0,
        stagesCompleted: 0,
        fallbackMethod: "basic",
      },
      fallbackUsed: true,
      fallbackReason: `Primary: ${context.fallbackReason}, Fallback: ${fallbackError.message}`,
    };
  }
};

/**
 * Map AI recommended dish IDs to actual dish objects
 */
const mapRecommendedDishes = (
  recommendedIds,
  semanticDishes,
  preFilteredDishes,
  maxCount = 5,
  allowFillFromSemantic = true
) => {
  const allDishes = [...(semanticDishes || []), ...(preFilteredDishes || [])];

  // If AI didn't recommend specific dishes, optionally fall back to semantic results
  if (!recommendedIds || recommendedIds.length === 0) {
    return allowFillFromSemantic ? (semanticDishes || []).slice(0, maxCount) : [];
  }

  // Map recommended IDs to dish objects, preserving order, no extras
  const recommendedDishes = [];
  const notFoundIds = [];

  recommendedIds.forEach((id) => {
    const dish = allDishes.find((d) => d._id.toString() === id.toString());
    if (
      dish &&
      !recommendedDishes.find((rd) => rd._id.toString() === dish._id.toString())
    ) {
      recommendedDishes.push(dish);
    } else if (!dish) {
      notFoundIds.push(id);
    }
  });

  // Log when AI recommended dishes that weren't found
  if (notFoundIds.length > 0) {
    console.log(`⚠️ AI recommended ${notFoundIds.length} dishes not found in semantic results:`, notFoundIds);
    console.log(`📊 Available dishes: ${allDishes.length}, Semantic dishes: ${semanticDishes?.length || 0}, Pre-filtered: ${preFilteredDishes?.length || 0}`);
  }

  return recommendedDishes.slice(0, maxCount);
};

/**
 * Update performance metrics
 */
const updatePerformanceMetrics = (
  responseTime,
  aiResult,
  preFilterResult,
  semanticResult
) => {
  // Update average response time
  performanceMetrics.averageResponseTime =
    (performanceMetrics.averageResponseTime *
      (performanceMetrics.totalRequests - 1) +
      responseTime) /
    performanceMetrics.totalRequests;

  // Calculate cache hit rate
  const cacheHits = [
    preFilterResult.fromCache,
    semanticResult.fromCache,
    aiResult.fromCache,
  ].filter(Boolean).length;

  const currentCacheRate = (cacheHits / 3) * 100;
  performanceMetrics.cacheHitRate =
    (performanceMetrics.cacheHitRate * (performanceMetrics.totalRequests - 1) +
      currentCacheRate) /
    performanceMetrics.totalRequests;

  // Estimate tokens saved (rough calculation)
  const estimatedOriginalTokens = 200 * 50; // Assuming 200 dishes * 50 tokens each
  const actualTokens = aiResult.tokenUsage.estimatedTokens || 0;
  performanceMetrics.tokensSaved += Math.max(
    0,
    estimatedOriginalTokens - actualTokens
  );
};

/**
 * Get performance metrics and statistics
 */
export const getPerformanceMetrics = () => {
  return {
    ...performanceMetrics,
    cacheStats: getCacheStats(),
    uptime: Date.now() - performanceMetrics.lastReset.getTime(),
    efficiency: {
      averageTokensSavedPerRequest:
        performanceMetrics.tokensSaved /
        Math.max(1, performanceMetrics.totalRequests),
      fallbackRate:
        (performanceMetrics.fallbackUsage /
          Math.max(1, performanceMetrics.totalRequests)) *
        100,
      successRate:
        ((performanceMetrics.totalRequests - performanceMetrics.fallbackUsage) /
          Math.max(1, performanceMetrics.totalRequests)) *
        100,
    },
  };
};

/**
 * Reset performance metrics
 */
export const resetPerformanceMetrics = () => {
  performanceMetrics = {
    totalRequests: 0,
    tokensSaved: 0,
    averageResponseTime: 0,
    cacheHitRate: 0,
    fallbackUsage: 0,
    lastReset: new Date(),
  };
};

/**
 * Health check for the optimization system
 */
export const healthCheck = async () => {
  try {
    const metrics = getPerformanceMetrics();
    const cacheStats = getCacheStats();

    return {
      status: "healthy",
      version: "1.0",
      metrics: {
        totalRequests: metrics.totalRequests,
        averageResponseTime: `${metrics.averageResponseTime.toFixed(2)}ms`,
        cacheHitRate: `${metrics.cacheHitRate.toFixed(1)}%`,
        successRate: `${metrics.efficiency.successRate.toFixed(1)}%`,
        fallbackRate: `${metrics.efficiency.fallbackRate.toFixed(1)}%`,
      },
      cacheHealth: {
        query: cacheStats.query.keys,
        preference: cacheStats.preference.keys,
        ai: cacheStats.ai.keys,
      },
      lastReset: metrics.lastReset,
    };
  } catch (error) {
    return {
      status: "error",
      error: error.message,
      timestamp: new Date(),
    };
  }
};

// Cache management exports
export { clearRecommendationCache };
