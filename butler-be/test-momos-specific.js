/**
 * Specific test for the momos recommendation issue
 * Tests that when user asks for momos, only momos are recommended
 */

import { optimizedPreFiltering } from './services/optimized-recommendation-service.js';

// Mock dishes similar to what might be in the actual database
const mockDishes = [
  {
    _id: "1",
    name: "Kurkure Momos",
    description: "This is a nepali dish made with indian kurkure. This is fast food deep fried and spiced.",
    category: { name: "appetizer" },
    tags: ["momos", "nepali", "fried"],
    isVeg: true,
    cuisine: "Nepali"
  },
  {
    _id: "2", 
    name: "Paneer Momos Steamed",
    description: "This is japanese dish. This cooked by steaming contains paneer",
    category: { name: "appetizer" },
    tags: ["momos", "steamed", "paneer"],
    isVeg: true,
    cuisine: "Japanese"
  },
  {
    _id: "3",
    name: "Paneer Momos Fried", 
    description: "This is paneer momos. Made with paneer and are deep fried in oil",
    category: { name: "appetizer" },
    tags: ["momos", "fried", "paneer"],
    isVeg: true,
    cuisine: "Tibetan"
  },
  {
    _id: "4",
    name: "Veg Hakka Noodles",
    description: "This is veg hakka noodles. Served hot and spicy, comes from thai province cuisine",
    category: { name: "main" },
    tags: ["noodles", "hakka", "veg"],
    isVeg: true,
    cuisine: "Thai"
  },
  {
    _id: "5",
    name: "Crispy Honey Potato",
    description: "This fries is includes both sweet and spicy taste. This is made from Potato using deep fried served with",
    category: { name: "appetizer" },
    tags: ["potato", "crispy", "honey"],
    isVeg: true,
    cuisine: "Continental"
  },
  {
    _id: "6",
    name: "Chicken Momos",
    description: "Delicious chicken momos with spicy filling",
    category: { name: "appetizer" },
    tags: ["momos", "chicken", "steamed"],
    isVeg: false,
    cuisine: "Tibetan"
  }
];

/**
 * Test the momos-specific filtering
 */
async function testMomosFiltering() {
  console.log("🧪 Testing Momos-Specific Filtering...\n");
  
  const testQueries = [
    "do you serve momos",
    "I want momos", 
    "show me momos",
    "momos please",
    "any momos available?"
  ];
  
  for (const query of testQueries) {
    console.log(`\n📝 Testing query: "${query}"`);
    
    try {
      const result = await optimizedPreFiltering(query, mockDishes, {});
      
      console.log(`📊 Filtered dishes: ${result.dishes.length}`);
      console.log(`📝 Dish names: ${result.dishes.map(d => d.name).join(", ")}`);
      
      // Validate results
      const allAreMomos = result.dishes.every(dish => {
        const dishName = dish.name.toLowerCase();
        const dishTags = (dish.tags || []).map(tag => tag.toLowerCase()).join(" ");
        return dishName.includes("momos") || dishTags.includes("momos");
      });
      
      const hasNonMomos = result.dishes.some(dish => {
        const dishName = dish.name.toLowerCase();
        const dishTags = (dish.tags || []).map(tag => tag.toLowerCase()).join(" ");
        return !dishName.includes("momos") && !dishTags.includes("momos");
      });
      
      if (allAreMomos && !hasNonMomos) {
        console.log("✅ PASS: Only momos returned");
      } else {
        console.log("❌ FAIL: Non-momos dishes included");
        
        // Show which dishes are not momos
        const nonMomosDishes = result.dishes.filter(dish => {
          const dishName = dish.name.toLowerCase();
          const dishTags = (dish.tags || []).map(tag => tag.toLowerCase()).join(" ");
          return !dishName.includes("momos") && !dishTags.includes("momos");
        });
        
        if (nonMomosDishes.length > 0) {
          console.log(`❌ Non-momos dishes: ${nonMomosDishes.map(d => d.name).join(", ")}`);
        }
      }
      
    } catch (error) {
      console.log("❌ Error:", error.message);
    }
  }
}

/**
 * Test edge cases
 */
async function testEdgeCases() {
  console.log("\n\n🧪 Testing Edge Cases...\n");
  
  const edgeCases = [
    {
      query: "momos and noodles",
      description: "Multiple specific dishes",
      expectBoth: true
    },
    {
      query: "something spicy",
      description: "General request",
      expectGeneral: true
    },
    {
      query: "vegetarian appetizers", 
      description: "Category request",
      expectCategory: true
    }
  ];
  
  for (const testCase of edgeCases) {
    console.log(`\n📝 Testing: ${testCase.description}`);
    console.log(`Query: "${testCase.query}"`);
    
    try {
      const result = await optimizedPreFiltering(testCase.query, mockDishes, {});
      
      console.log(`📊 Filtered dishes: ${result.dishes.length}`);
      console.log(`📝 Dish names: ${result.dishes.map(d => d.name).join(", ")}`);
      
      if (testCase.expectBoth) {
        const hasMomos = result.dishes.some(d => d.name.toLowerCase().includes("momos"));
        const hasNoodles = result.dishes.some(d => d.name.toLowerCase().includes("noodles"));
        
        if (hasMomos && hasNoodles) {
          console.log("✅ PASS: Both momos and noodles included");
        } else {
          console.log("❌ FAIL: Missing requested dish types");
        }
      } else {
        console.log("✅ Edge case processed");
      }
      
    } catch (error) {
      console.log("❌ Error:", error.message);
    }
  }
}

// Run tests
async function runAllTests() {
  await testMomosFiltering();
  await testEdgeCases();
  
  console.log("\n🎉 Momos-specific tests completed!");
}

// Execute if run directly
if (import.meta.url === `file://${process.argv[1]}`) {
  runAllTests().catch(console.error);
}

export { testMomosFiltering, testEdgeCases };
