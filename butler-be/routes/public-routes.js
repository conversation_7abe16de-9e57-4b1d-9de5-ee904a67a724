import express from "express";
import {
  adminLogin,
  superAdminLogin,
  getTheme,
  forgetPassword,
  resetPassword,
  submitFoodChainRegistrationRequest,
  getPublicFoodChainData,
} from "../controllers/public-controller.js";
import { sendTestEmail } from "../utils/emailService.js";
import { logout } from "../middlewares/auth.js";
import { authenticateToken, authenticateSuperAdminToken } from "../middlewares/auth.js";
import { validateAdminLogin } from "../middlewares/validation.js";

const router = express.Router();

// Authentication routes
router.post("/admin/login", validateAdminLogin, adminLogin);
router.post("/super-admin/login", validateAdminLogin, superAdminLogin);

// Logout routes
router.post("/admin/logout", authenticateToken, logout);
router.post("/super-admin/logout", authenticateSuperAdminToken, logout);

// Password reset routes
router.post("/forget-password", forgetPassword);
router.post("/reset-password", resetPassword);

// Theme routes
router.get("/get-theme/:foodChainId", getTheme);

// Public Food Chain Data routes
router.get("/public/foodchain/:foodchainId", getPublicFoodChainData);

// Food Chain Registration Request routes
router.post(
  "/food-chain-registration-request",
  submitFoodChainRegistrationRequest
);

// Test email route
router.get("/test-email", async (req, res) => {
  try {
    const { email = "<EMAIL>" } = req.query;
    const result = await sendTestEmail(email);

    if (result.success) {
      res.status(200).json({
        success: true,
        message: `Test email sent successfully to ${email}`,
        messageId: result.messageId,
      });
    } else {
      res.status(500).json({
        success: false,
        message: "Failed to send test email",
        error: result.error,
      });
    }
  } catch (error) {
    console.error("Error in test-email route:", error);
    res.status(500).json({
      success: false,
      message: "Error sending test email",
      error: error.message,
    });
  }
});

export default router;
