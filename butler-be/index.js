import express from "express";
import { createServer } from "http";
import { Server } from "socket.io";
import { setupOrderSocket } from "./sockets/orderSocket.js";
import { setupCartSocket } from "./sockets/cartSocket.js";
import bodyParser from "body-parser";
import { config } from "dotenv";
import connectDB from "./config/database.js";
import { setupSubscriptionCronJobs } from "./utils/subscriptionCron.js";
import { setupWebhookRetryCronJob } from "./utils/webhookRetryCron.js";
import { setupPaymentMonitoringCronJob } from "./utils/paymentMonitoringCron.js";
import adminRoutes from "./routes/admin-routes.js";
import authRoutes from "./routes/public-routes.js";
import userRoutes from "./routes/user-routes.js";
import superAdminRoutes from "./routes/super-admin-routes.js";
import paymentRoutes from "./routes/payment-routes.js";
import fundTransferRoutes from "./routes/fund-transfer-routes.js";
import subscriptionRoutes from "./routes/subscription-routes.js";
import notificationRoutes from "./routes/notification-routes.js";
import marketingRoutes from "./routes/marketing-routes.js";
import optimizationRoutes from "./routes/optimization-routes.js";
import cartRoutes from "./routes/cart-routes.js";
import audioTranscriptionRoutes from "./routes/audio-transcription-routes.js";
import monitoringRoutes from "./routes/monitoring-routes.js";
import cors from "cors";
import passport from "passport";
import session from "express-session";

// Import security middlewares
import {
  securityHeaders,
  mongoSanitization,
  xssProtection,
  requestLogger,
  responseCompression,
  corsOptions,
  securityAudit,
  apiRateLimit,
  authRateLimit
} from "./middlewares/security.js";

// Import error handling and logging
import {
  globalErrorHandler,
  notFoundHandler,
  performanceMonitor,
  healthCheck
} from "./middlewares/errorHandler.js";
import logger, { logInfo, logError } from "./utils/logger.js";

// Import performance optimizations
import { initializeDatabaseOptimizations } from "./utils/database-optimization.js";
import cacheManager from "./utils/cache.js";

config();

// Validate required environment variables
const requiredEnvVars = [
  'JWT_SECRET',
  'JWT_SECRET_SUPER_ADMIN',
  'MONGODB_URI'
];

const missingEnvVars = requiredEnvVars.filter(envVar => !process.env[envVar]);
if (missingEnvVars.length > 0) {
  logError(new Error('Missing required environment variables'), { missingEnvVars });
  process.exit(1);
}

connectDB();

const app = express();
const httpServer = createServer(app);

// Trust proxy for accurate IP addresses
app.set('trust proxy', 1);

// Security headers (must be first)
app.use(securityHeaders);

// Request compression
app.use(responseCompression);

// Performance monitoring
app.use(performanceMonitor(2000)); // Alert for requests taking more than 2 seconds

// Request logging
if (process.env.NODE_ENV !== 'test') {
  app.use(requestLogger);
}

// CORS configuration
app.use(cors(corsOptions));

const io = new Server(httpServer, {
  cors: corsOptions
});

// Make io globally available
global.io = io;

// Setup socket handlers
setupOrderSocket(io);
setupCartSocket(io);

// Body parsing with size limits
app.use(bodyParser.json({
  limit: '10mb',
  verify: (req, res, buf) => {
    req.rawBody = buf;
  }
}));
app.use(bodyParser.urlencoded({
  extended: true,
  limit: '10mb'
}));

// Security middlewares
app.use(mongoSanitization);
app.use(xssProtection);
app.use(securityAudit);

// Session configuration for passport
app.use(
  session({
    secret: process.env.JWT_SECRET,
    resave: false,
    saveUninitialized: false,
    name: 'butler.sid', // Change default session name
    cookie: {
      secure: process.env.NODE_ENV === 'production', // HTTPS only in production
      httpOnly: true, // Prevent XSS
      maxAge: 24 * 60 * 60 * 1000, // 24 hours
      sameSite: 'strict' // CSRF protection
    },
  })
);

// Initialize passport
app.use(passport.initialize());
app.use(passport.session());

// Health check endpoint (no rate limiting)
app.get("/health", healthCheck);

// Root endpoint
app.get("/", (req, res) => {
  res.json({
    success: true,
    message: "Welcome to Butler API",
    version: "1.0.0",
    documentation: "/api/docs"
  });
});

// Apply rate limiting to API routes
app.use("/api/v1/admin/login", authRateLimit);
app.use("/api/v1/super-admin/login", authRateLimit);
app.use("/api/v1/user/register", authRateLimit);
app.use("/api/v1/user/login", authRateLimit);
app.use("/api/v1", apiRateLimit);

// Routes
app.use("/api/v1", authRoutes);
app.use("/api/transcribe-audio", audioTranscriptionRoutes);
app.use("/api/v1/monitoring", monitoringRoutes);
app.use(
  "/api/v1",
  adminRoutes,
  superAdminRoutes,
  userRoutes,
  paymentRoutes,
  fundTransferRoutes,
  subscriptionRoutes,
  notificationRoutes,
  marketingRoutes,
  optimizationRoutes,
  cartRoutes
);

// 404 handler
app.use('*', notFoundHandler);

// Global error handler
app.use(globalErrorHandler);

// Graceful shutdown handling
const gracefulShutdown = async (signal) => {
  logInfo(`Received ${signal}. Starting graceful shutdown...`);

  httpServer.close(async () => {
    logInfo('HTTP server closed.');

    // Close cache connection
    try {
      await cacheManager.disconnect();
      logInfo('Cache connection closed.');
    } catch (error) {
      logError(error, { context: 'cache_shutdown' });
    }

    // Close database connection
    if (global.mongoose && global.mongoose.connection) {
      global.mongoose.connection.close(() => {
        logInfo('Database connection closed.');
        process.exit(0);
      });
    } else {
      process.exit(0);
    }
  });

  // Force close after 30 seconds
  setTimeout(() => {
    logError(new Error('Could not close connections in time, forcefully shutting down'));
    process.exit(1);
  }, 30000);
};

// Listen for termination signals
process.on('SIGTERM', () => gracefulShutdown('SIGTERM'));
process.on('SIGINT', () => gracefulShutdown('SIGINT'));

// Handle uncaught exceptions
process.on('uncaughtException', (error) => {
  logError(error, { context: 'uncaught_exception' });
  gracefulShutdown('uncaughtException');
});

// Handle unhandled promise rejections
process.on('unhandledRejection', (reason, promise) => {
  logError(new Error('Unhandled Promise Rejection'), {
    reason: reason.toString(),
    promise: promise.toString(),
    context: 'unhandled_rejection'
  });
  gracefulShutdown('unhandledRejection');
});

const PORT = process.env.PORT || 3001;

httpServer.listen(PORT, async () => {
  logInfo(`🚀 Butler API server running on port ${PORT}`);
  logInfo(`📊 Environment: ${process.env.NODE_ENV || 'development'}`);
  logInfo(`🔒 Security: Enhanced security measures active`);
  logInfo(`📈 Health check: http://localhost:${PORT}/health`);
  logInfo(`📝 Logging: Winston logger initialized`);

  // Initialize performance optimizations
  try {
    await initializeDatabaseOptimizations();
    await cacheManager.initialize();
    logInfo("⚡ Performance optimizations initialized");
  } catch (error) {
    logError(error, { context: 'performance_initialization' });
  }

  // Setup cron jobs
  setupSubscriptionCronJobs();
  setupWebhookRetryCronJob();
  setupPaymentMonitoringCronJob();
  logInfo("⏰ Cron jobs initialized");
});
