{
  message: {
    event: 'unauthorized_access_attempt',
    method: 'POST',
    url: '/api/v1/user/create-order',
    ip: '::1',
    userAgent: 'curl/8.7.1',
    statusCode: 401,
    timestamp: '2025-11-20T18:39:31.610Z'
  },
  level: 'warn',
  timestamp: '2025-11-21 00:09:31:931'
}
{
  message: {
    event: 'unauthorized_access_attempt',
    method: 'POST',
    url: '/api/v1/user/create-order',
    ip: '::1',
    userAgent: 'curl/8.7.1',
    statusCode: 401,
    timestamp: '2025-11-20T18:39:40.133Z'
  },
  level: 'warn',
  timestamp: '2025-11-21 00:09:40:940'
}
{
  message: {
    event: 'unauthorized_access_attempt',
    method: 'POST',
    url: '/api/v1/user/create-order',
    ip: '::1',
    userAgent: 'curl/8.7.1',
    statusCode: 401,
    timestamp: '2025-11-20T18:40:03.822Z'
  },
  level: 'warn',
  timestamp: '2025-11-21 00:10:03:103'
}
{
  message: {
    event: 'unauthorized_access_attempt',
    method: 'POST',
    url: '/api/v1/user/create-order',
    ip: '::1',
    userAgent: 'curl/8.7.1',
    statusCode: 401,
    timestamp: '2025-11-20T18:40:11.250Z'
  },
  level: 'warn',
  timestamp: '2025-11-21 00:10:11:1011'
}
{
  message: {
    event: 'endpoint_not_found',
    url: '/api/v1/user/test-order-validation',
    method: 'POST',
    ip: '::1',
    userAgent: 'curl/8.7.1',
    timestamp: '2025-11-20T18:40:30.033Z'
  },
  level: 'warn',
  timestamp: '2025-11-21 00:10:30:1030'
}
{
  message: {
    event: 'endpoint_not_found',
    url: '/api/v1/user/personalized-menu?foodChainId=6899f5527403b47ea1df69a2&outletId=6899fa5358dbcc8f5ced8116',
    method: 'GET',
    ip: '::1',
    userAgent: 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
    timestamp: '2025-11-20T18:48:40.724Z'
  },
  level: 'warn',
  timestamp: '2025-11-21 00:18:40:1840'
}
{
  message: {
    event: 'endpoint_not_found',
    url: '/api/v1/user/personalized-menu?foodChainId=6899f5527403b47ea1df69a2&outletId=6899fa5358dbcc8f5ced8116',
    method: 'GET',
    ip: '::1',
    userAgent: 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
    timestamp: '2025-11-20T18:48:40.726Z'
  },
  level: 'warn',
  timestamp: '2025-11-21 00:18:40:1840'
}
