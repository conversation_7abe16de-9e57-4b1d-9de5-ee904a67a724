{
  message: "Cannot read properties of undefined (reading 'collection')",
  stack: "TypeError: Cannot read properties of undefined (reading 'collection')\n" +
    '    at createDatabaseIndexes (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:115:31)\n' +
    '    at initializeDatabaseOptimizations (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:359:11)\n' +
    '    at Server.<anonymous> (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/index.js:251:11)\n' +
    '    at Object.onceWrapper (node:events:632:28)\n' +
    '    at Server.emit (node:events:530:35)\n' +
    '    at emitListeningNT (node:net:1983:10)\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:89:21)',
  context: 'index_creation',
  collection: 'users',
  timestamp: '2025-11-22 02:22:15:2215',
  level: 'error',
  service: 'butler-api',
  environment: 'development'
}
{
  message: "Cannot read properties of undefined (reading 'collection')",
  stack: "TypeError: Cannot read properties of undefined (reading 'collection')\n" +
    '    at createDatabaseIndexes (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:115:31)\n' +
    '    at initializeDatabaseOptimizations (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:359:11)\n' +
    '    at Server.<anonymous> (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/index.js:251:11)\n' +
    '    at Object.onceWrapper (node:events:632:28)\n' +
    '    at Server.emit (node:events:530:35)\n' +
    '    at emitListeningNT (node:net:1983:10)\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:89:21)',
  context: 'index_creation',
  collection: 'orders',
  timestamp: '2025-11-22 02:22:15:2215',
  level: 'error',
  service: 'butler-api',
  environment: 'development'
}
{
  message: "Cannot read properties of undefined (reading 'collection')",
  stack: "TypeError: Cannot read properties of undefined (reading 'collection')\n" +
    '    at createDatabaseIndexes (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:115:31)\n' +
    '    at initializeDatabaseOptimizations (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:359:11)\n' +
    '    at Server.<anonymous> (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/index.js:251:11)\n' +
    '    at Object.onceWrapper (node:events:632:28)\n' +
    '    at Server.emit (node:events:530:35)\n' +
    '    at emitListeningNT (node:net:1983:10)\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:89:21)',
  context: 'index_creation',
  collection: 'dishes',
  timestamp: '2025-11-22 02:22:15:2215',
  level: 'error',
  service: 'butler-api',
  environment: 'development'
}
{
  message: "Cannot read properties of undefined (reading 'collection')",
  stack: "TypeError: Cannot read properties of undefined (reading 'collection')\n" +
    '    at createDatabaseIndexes (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:115:31)\n' +
    '    at initializeDatabaseOptimizations (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:359:11)\n' +
    '    at Server.<anonymous> (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/index.js:251:11)\n' +
    '    at Object.onceWrapper (node:events:632:28)\n' +
    '    at Server.emit (node:events:530:35)\n' +
    '    at emitListeningNT (node:net:1983:10)\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:89:21)',
  context: 'index_creation',
  collection: 'outlets',
  timestamp: '2025-11-22 02:22:15:2215',
  level: 'error',
  service: 'butler-api',
  environment: 'development'
}
{
  message: "Cannot read properties of undefined (reading 'collection')",
  stack: "TypeError: Cannot read properties of undefined (reading 'collection')\n" +
    '    at createDatabaseIndexes (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:115:31)\n' +
    '    at initializeDatabaseOptimizations (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:359:11)\n' +
    '    at Server.<anonymous> (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/index.js:251:11)\n' +
    '    at Object.onceWrapper (node:events:632:28)\n' +
    '    at Server.emit (node:events:530:35)\n' +
    '    at emitListeningNT (node:net:1983:10)\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:89:21)',
  context: 'index_creation',
  collection: 'carts',
  timestamp: '2025-11-22 02:22:15:2215',
  level: 'error',
  service: 'butler-api',
  environment: 'development'
}
{
  message: "Cannot read properties of undefined (reading 'collection')",
  stack: "TypeError: Cannot read properties of undefined (reading 'collection')\n" +
    '    at createDatabaseIndexes (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:115:31)\n' +
    '    at initializeDatabaseOptimizations (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:359:11)\n' +
    '    at Server.<anonymous> (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/index.js:251:11)\n' +
    '    at Object.onceWrapper (node:events:632:28)\n' +
    '    at Server.emit (node:events:530:35)\n' +
    '    at emitListeningNT (node:net:1983:10)\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:89:21)',
  context: 'index_creation',
  collection: 'payments',
  timestamp: '2025-11-22 02:22:15:2215',
  level: 'error',
  service: 'butler-api',
  environment: 'development'
}
{
  message: "Cannot read properties of undefined (reading 'collection')",
  stack: "TypeError: Cannot read properties of undefined (reading 'collection')\n" +
    '    at createDatabaseIndexes (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:115:31)\n' +
    '    at initializeDatabaseOptimizations (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:359:11)\n' +
    '    at Server.<anonymous> (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/index.js:251:11)\n' +
    '    at Object.onceWrapper (node:events:632:28)\n' +
    '    at Server.emit (node:events:530:35)\n' +
    '    at emitListeningNT (node:net:1983:10)\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:89:21)',
  context: 'index_creation',
  collection: 'notifications',
  timestamp: '2025-11-22 02:22:15:2215',
  level: 'error',
  service: 'butler-api',
  environment: 'development'
}
{
  message: "Cannot read properties of undefined (reading 'collection')",
  stack: "TypeError: Cannot read properties of undefined (reading 'collection')\n" +
    '    at createDatabaseIndexes (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:115:31)\n' +
    '    at initializeDatabaseOptimizations (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:359:11)\n' +
    '    at Server.<anonymous> (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/index.js:251:11)\n' +
    '    at Object.onceWrapper (node:events:632:28)\n' +
    '    at Server.emit (node:events:530:35)\n' +
    '    at emitListeningNT (node:net:1983:10)\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:89:21)',
  context: 'index_creation',
  collection: 'subscriptions',
  timestamp: '2025-11-22 02:22:15:2215',
  level: 'error',
  service: 'butler-api',
  environment: 'development'
}
{
  message: "Cannot read properties of undefined (reading 'collection')",
  stack: "TypeError: Cannot read properties of undefined (reading 'collection')\n" +
    '    at createDatabaseIndexes (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:115:31)\n' +
    '    at initializeDatabaseOptimizations (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:359:11)\n' +
    '    at Server.<anonymous> (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/index.js:251:11)\n' +
    '    at Object.onceWrapper (node:events:632:28)\n' +
    '    at Server.emit (node:events:530:35)\n' +
    '    at emitListeningNT (node:net:1983:10)\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:89:21)',
  context: 'index_creation',
  collection: 'customerfeedbacks',
  timestamp: '2025-11-22 02:22:15:2215',
  level: 'error',
  service: 'butler-api',
  environment: 'development'
}
{
  message: "Cannot read properties of undefined (reading 'collection')",
  stack: "TypeError: Cannot read properties of undefined (reading 'collection')\n" +
    '    at createDatabaseIndexes (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:115:31)\n' +
    '    at initializeDatabaseOptimizations (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:359:11)\n' +
    '    at Server.<anonymous> (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/index.js:251:11)\n' +
    '    at Object.onceWrapper (node:events:632:28)\n' +
    '    at Server.emit (node:events:530:35)\n' +
    '    at emitListeningNT (node:net:1983:10)\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:89:21)',
  context: 'index_creation',
  collection: 'conversations',
  timestamp: '2025-11-22 02:22:15:2215',
  level: 'error',
  service: 'butler-api',
  environment: 'development'
}
{
  message: "Cannot read properties of undefined (reading 'collection')",
  stack: "TypeError: Cannot read properties of undefined (reading 'collection')\n" +
    '    at createDatabaseIndexes (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:115:31)\n' +
    '    at initializeDatabaseOptimizations (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:359:11)\n' +
    '    at Server.<anonymous> (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/index.js:251:11)\n' +
    '    at Object.onceWrapper (node:events:632:28)\n' +
    '    at Server.emit (node:events:530:35)\n' +
    '    at emitListeningNT (node:net:1983:10)\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:89:21)',
  context: 'index_creation',
  collection: 'auditlogs',
  timestamp: '2025-11-22 02:22:15:2215',
  level: 'error',
  service: 'butler-api',
  environment: 'development'
}
