{
  message: '🚀 Butler API server running on port 3001',
  timestamp: '2025-11-21 00:08:04:84',
  level: 'info',
  service: 'butler-api',
  environment: 'development'
}
{
  message: '📊 Environment: development',
  timestamp: '2025-11-21 00:08:04:84',
  level: 'info',
  service: 'butler-api',
  environment: 'development'
}
{
  message: '🔒 Security: Enhanced security measures active',
  timestamp: '2025-11-21 00:08:04:84',
  level: 'info',
  service: 'butler-api',
  environment: 'development'
}
{
  message: '📈 Health check: http://localhost:3001/health',
  timestamp: '2025-11-21 00:08:04:84',
  level: 'info',
  service: 'butler-api',
  environment: 'development'
}
{
  message: '📝 Logging: <PERSON> logger initialized',
  timestamp: '2025-11-21 00:08:04:84',
  level: 'info',
  service: 'butler-api',
  environment: 'development'
}
{
  message: 'Initializing database optimizations...',
  timestamp: '2025-11-21 00:08:04:84',
  level: 'info',
  service: 'butler-api',
  environment: 'development'
}
{
  message: 'Database connection optimized',
  timestamp: '2025-11-21 00:08:04:84',
  level: 'info',
  service: 'butler-api',
  environment: 'development'
}
{
  message: 'Starting database index creation...',
  timestamp: '2025-11-21 00:08:04:84',
  level: 'info',
  service: 'butler-api',
  environment: 'development'
}
{
  message: "Cannot read properties of undefined (reading 'collection')",
  stack: "TypeError: Cannot read properties of undefined (reading 'collection')\n" +
    '    at createDatabaseIndexes (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:115:31)\n' +
    '    at initializeDatabaseOptimizations (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:359:11)\n' +
    '    at Server.<anonymous> (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/index.js:251:11)\n' +
    '    at Object.onceWrapper (node:events:632:28)\n' +
    '    at Server.emit (node:events:530:35)\n' +
    '    at emitListeningNT (node:net:1983:10)\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:89:21)',
  context: 'index_creation',
  collection: 'users',
  timestamp: '2025-11-21 00:08:04:84',
  level: 'error',
  service: 'butler-api',
  environment: 'development'
}
{
  message: "Cannot read properties of undefined (reading 'collection')",
  stack: "TypeError: Cannot read properties of undefined (reading 'collection')\n" +
    '    at createDatabaseIndexes (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:115:31)\n' +
    '    at initializeDatabaseOptimizations (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:359:11)\n' +
    '    at Server.<anonymous> (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/index.js:251:11)\n' +
    '    at Object.onceWrapper (node:events:632:28)\n' +
    '    at Server.emit (node:events:530:35)\n' +
    '    at emitListeningNT (node:net:1983:10)\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:89:21)',
  context: 'index_creation',
  collection: 'orders',
  timestamp: '2025-11-21 00:08:04:84',
  level: 'error',
  service: 'butler-api',
  environment: 'development'
}
{
  message: "Cannot read properties of undefined (reading 'collection')",
  stack: "TypeError: Cannot read properties of undefined (reading 'collection')\n" +
    '    at createDatabaseIndexes (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:115:31)\n' +
    '    at initializeDatabaseOptimizations (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:359:11)\n' +
    '    at Server.<anonymous> (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/index.js:251:11)\n' +
    '    at Object.onceWrapper (node:events:632:28)\n' +
    '    at Server.emit (node:events:530:35)\n' +
    '    at emitListeningNT (node:net:1983:10)\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:89:21)',
  context: 'index_creation',
  collection: 'dishes',
  timestamp: '2025-11-21 00:08:04:84',
  level: 'error',
  service: 'butler-api',
  environment: 'development'
}
{
  message: "Cannot read properties of undefined (reading 'collection')",
  stack: "TypeError: Cannot read properties of undefined (reading 'collection')\n" +
    '    at createDatabaseIndexes (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:115:31)\n' +
    '    at initializeDatabaseOptimizations (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:359:11)\n' +
    '    at Server.<anonymous> (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/index.js:251:11)\n' +
    '    at Object.onceWrapper (node:events:632:28)\n' +
    '    at Server.emit (node:events:530:35)\n' +
    '    at emitListeningNT (node:net:1983:10)\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:89:21)',
  context: 'index_creation',
  collection: 'outlets',
  timestamp: '2025-11-21 00:08:04:84',
  level: 'error',
  service: 'butler-api',
  environment: 'development'
}
{
  message: "Cannot read properties of undefined (reading 'collection')",
  stack: "TypeError: Cannot read properties of undefined (reading 'collection')\n" +
    '    at createDatabaseIndexes (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:115:31)\n' +
    '    at initializeDatabaseOptimizations (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:359:11)\n' +
    '    at Server.<anonymous> (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/index.js:251:11)\n' +
    '    at Object.onceWrapper (node:events:632:28)\n' +
    '    at Server.emit (node:events:530:35)\n' +
    '    at emitListeningNT (node:net:1983:10)\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:89:21)',
  context: 'index_creation',
  collection: 'carts',
  timestamp: '2025-11-21 00:08:04:84',
  level: 'error',
  service: 'butler-api',
  environment: 'development'
}
{
  message: "Cannot read properties of undefined (reading 'collection')",
  stack: "TypeError: Cannot read properties of undefined (reading 'collection')\n" +
    '    at createDatabaseIndexes (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:115:31)\n' +
    '    at initializeDatabaseOptimizations (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:359:11)\n' +
    '    at Server.<anonymous> (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/index.js:251:11)\n' +
    '    at Object.onceWrapper (node:events:632:28)\n' +
    '    at Server.emit (node:events:530:35)\n' +
    '    at emitListeningNT (node:net:1983:10)\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:89:21)',
  context: 'index_creation',
  collection: 'payments',
  timestamp: '2025-11-21 00:08:04:84',
  level: 'error',
  service: 'butler-api',
  environment: 'development'
}
{
  message: "Cannot read properties of undefined (reading 'collection')",
  stack: "TypeError: Cannot read properties of undefined (reading 'collection')\n" +
    '    at createDatabaseIndexes (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:115:31)\n' +
    '    at initializeDatabaseOptimizations (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:359:11)\n' +
    '    at Server.<anonymous> (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/index.js:251:11)\n' +
    '    at Object.onceWrapper (node:events:632:28)\n' +
    '    at Server.emit (node:events:530:35)\n' +
    '    at emitListeningNT (node:net:1983:10)\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:89:21)',
  context: 'index_creation',
  collection: 'notifications',
  timestamp: '2025-11-21 00:08:04:84',
  level: 'error',
  service: 'butler-api',
  environment: 'development'
}
{
  message: "Cannot read properties of undefined (reading 'collection')",
  stack: "TypeError: Cannot read properties of undefined (reading 'collection')\n" +
    '    at createDatabaseIndexes (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:115:31)\n' +
    '    at initializeDatabaseOptimizations (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:359:11)\n' +
    '    at Server.<anonymous> (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/index.js:251:11)\n' +
    '    at Object.onceWrapper (node:events:632:28)\n' +
    '    at Server.emit (node:events:530:35)\n' +
    '    at emitListeningNT (node:net:1983:10)\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:89:21)',
  context: 'index_creation',
  collection: 'subscriptions',
  timestamp: '2025-11-21 00:08:04:84',
  level: 'error',
  service: 'butler-api',
  environment: 'development'
}
{
  message: "Cannot read properties of undefined (reading 'collection')",
  stack: "TypeError: Cannot read properties of undefined (reading 'collection')\n" +
    '    at createDatabaseIndexes (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:115:31)\n' +
    '    at initializeDatabaseOptimizations (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:359:11)\n' +
    '    at Server.<anonymous> (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/index.js:251:11)\n' +
    '    at Object.onceWrapper (node:events:632:28)\n' +
    '    at Server.emit (node:events:530:35)\n' +
    '    at emitListeningNT (node:net:1983:10)\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:89:21)',
  context: 'index_creation',
  collection: 'customerfeedbacks',
  timestamp: '2025-11-21 00:08:04:84',
  level: 'error',
  service: 'butler-api',
  environment: 'development'
}
{
  message: "Cannot read properties of undefined (reading 'collection')",
  stack: "TypeError: Cannot read properties of undefined (reading 'collection')\n" +
    '    at createDatabaseIndexes (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:115:31)\n' +
    '    at initializeDatabaseOptimizations (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:359:11)\n' +
    '    at Server.<anonymous> (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/index.js:251:11)\n' +
    '    at Object.onceWrapper (node:events:632:28)\n' +
    '    at Server.emit (node:events:530:35)\n' +
    '    at emitListeningNT (node:net:1983:10)\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:89:21)',
  context: 'index_creation',
  collection: 'conversations',
  timestamp: '2025-11-21 00:08:04:84',
  level: 'error',
  service: 'butler-api',
  environment: 'development'
}
{
  message: "Cannot read properties of undefined (reading 'collection')",
  stack: "TypeError: Cannot read properties of undefined (reading 'collection')\n" +
    '    at createDatabaseIndexes (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:115:31)\n' +
    '    at initializeDatabaseOptimizations (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:359:11)\n' +
    '    at Server.<anonymous> (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/index.js:251:11)\n' +
    '    at Object.onceWrapper (node:events:632:28)\n' +
    '    at Server.emit (node:events:530:35)\n' +
    '    at emitListeningNT (node:net:1983:10)\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:89:21)',
  context: 'index_creation',
  collection: 'auditlogs',
  timestamp: '2025-11-21 00:08:04:84',
  level: 'error',
  service: 'butler-api',
  environment: 'development'
}
{
  message: 'Database index creation completed',
  timestamp: '2025-11-21 00:08:04:84',
  level: 'info',
  service: 'butler-api',
  environment: 'development'
}
{
  message: 'Database optimizations initialized successfully',
  timestamp: '2025-11-21 00:08:04:84',
  level: 'info',
  service: 'butler-api',
  environment: 'development'
}
{
  message: 'Redis not configured, using in-memory cache fallback',
  timestamp: '2025-11-21 00:08:04:84',
  level: 'info',
  service: 'butler-api',
  environment: 'development'
}
{
  message: '⚡ Performance optimizations initialized',
  timestamp: '2025-11-21 00:08:04:84',
  level: 'info',
  service: 'butler-api',
  environment: 'development'
}
{
  message: '⏰ Cron jobs initialized',
  timestamp: '2025-11-21 00:08:04:84',
  level: 'info',
  service: 'butler-api',
  environment: 'development'
}
{
  message: {
    method: 'POST',
    url: '/api/v1/user/create-order',
    statusCode: 401,
    responseTime: '15ms',
    ip: '::1',
    userAgent: 'curl/8.7.1',
    userId: 'anonymous',
    contentLength: '43',
    timestamp: '2025-11-20T18:39:31.609Z'
  },
  level: 'http',
  timestamp: '2025-11-21 00:09:31:931'
}
{
  message: {
    method: 'POST',
    url: '/api/v1/user/create-order',
    statusCode: 401,
    responseTime: '2ms',
    ip: '::1',
    userAgent: 'curl/8.7.1',
    userId: 'anonymous',
    contentLength: '43',
    timestamp: '2025-11-20T18:39:40.133Z'
  },
  level: 'http',
  timestamp: '2025-11-21 00:09:40:940'
}
{
  message: {
    method: 'POST',
    url: '/api/v1/user/create-order',
    statusCode: 401,
    responseTime: '1ms',
    ip: '::1',
    userAgent: 'curl/8.7.1',
    userId: 'anonymous',
    contentLength: '47',
    timestamp: '2025-11-20T18:40:03.822Z'
  },
  level: 'http',
  timestamp: '2025-11-21 00:10:03:103'
}
{
  message: {
    method: 'POST',
    url: '/api/v1/user/create-order',
    statusCode: 401,
    responseTime: '1ms',
    ip: '::1',
    userAgent: 'curl/8.7.1',
    userId: 'anonymous',
    contentLength: '43',
    timestamp: '2025-11-20T18:40:11.250Z'
  },
  level: 'http',
  timestamp: '2025-11-21 00:10:11:1011'
}
{
  message: "Can't find /api/v1/user/test-order-validation on this server!",
  stack: undefined,
  url: '/api/v1/user/test-order-validation',
  method: 'POST',
  ip: '::1',
  userAgent: 'curl/8.7.1',
  userId: 'anonymous',
  timestamp: '2025-11-21 00:10:30:1030',
  level: 'error',
  service: 'butler-api',
  environment: 'development'
}
{
  message: {
    method: 'POST',
    url: '/api/v1/user/test-order-validation',
    statusCode: 404,
    responseTime: '4ms',
    ip: '::1',
    userAgent: 'curl/8.7.1',
    userId: 'anonymous',
    contentLength: '130',
    timestamp: '2025-11-20T18:40:30.034Z'
  },
  level: 'http',
  timestamp: '2025-11-21 00:10:30:1030'
}
{
  message: '🚀 Butler API server running on port 3001',
  timestamp: '2025-11-21 00:10:45:1045',
  level: 'info',
  service: 'butler-api',
  environment: 'development'
}
{
  message: '📊 Environment: development',
  timestamp: '2025-11-21 00:10:45:1045',
  level: 'info',
  service: 'butler-api',
  environment: 'development'
}
{
  message: '🔒 Security: Enhanced security measures active',
  timestamp: '2025-11-21 00:10:45:1045',
  level: 'info',
  service: 'butler-api',
  environment: 'development'
}
{
  message: '📈 Health check: http://localhost:3001/health',
  timestamp: '2025-11-21 00:10:45:1045',
  level: 'info',
  service: 'butler-api',
  environment: 'development'
}
{
  message: '📝 Logging: Winston logger initialized',
  timestamp: '2025-11-21 00:10:45:1045',
  level: 'info',
  service: 'butler-api',
  environment: 'development'
}
{
  message: 'Initializing database optimizations...',
  timestamp: '2025-11-21 00:10:45:1045',
  level: 'info',
  service: 'butler-api',
  environment: 'development'
}
{
  message: 'Database connection optimized',
  timestamp: '2025-11-21 00:10:45:1045',
  level: 'info',
  service: 'butler-api',
  environment: 'development'
}
{
  message: 'Starting database index creation...',
  timestamp: '2025-11-21 00:10:45:1045',
  level: 'info',
  service: 'butler-api',
  environment: 'development'
}
{
  message: "Cannot read properties of undefined (reading 'collection')",
  stack: "TypeError: Cannot read properties of undefined (reading 'collection')\n" +
    '    at createDatabaseIndexes (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:115:31)\n' +
    '    at initializeDatabaseOptimizations (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:359:11)\n' +
    '    at Server.<anonymous> (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/index.js:251:11)\n' +
    '    at Object.onceWrapper (node:events:632:28)\n' +
    '    at Server.emit (node:events:530:35)\n' +
    '    at emitListeningNT (node:net:1983:10)\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:89:21)',
  context: 'index_creation',
  collection: 'users',
  timestamp: '2025-11-21 00:10:45:1045',
  level: 'error',
  service: 'butler-api',
  environment: 'development'
}
{
  message: "Cannot read properties of undefined (reading 'collection')",
  stack: "TypeError: Cannot read properties of undefined (reading 'collection')\n" +
    '    at createDatabaseIndexes (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:115:31)\n' +
    '    at initializeDatabaseOptimizations (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:359:11)\n' +
    '    at Server.<anonymous> (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/index.js:251:11)\n' +
    '    at Object.onceWrapper (node:events:632:28)\n' +
    '    at Server.emit (node:events:530:35)\n' +
    '    at emitListeningNT (node:net:1983:10)\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:89:21)',
  context: 'index_creation',
  collection: 'orders',
  timestamp: '2025-11-21 00:10:45:1045',
  level: 'error',
  service: 'butler-api',
  environment: 'development'
}
{
  message: "Cannot read properties of undefined (reading 'collection')",
  stack: "TypeError: Cannot read properties of undefined (reading 'collection')\n" +
    '    at createDatabaseIndexes (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:115:31)\n' +
    '    at initializeDatabaseOptimizations (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:359:11)\n' +
    '    at Server.<anonymous> (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/index.js:251:11)\n' +
    '    at Object.onceWrapper (node:events:632:28)\n' +
    '    at Server.emit (node:events:530:35)\n' +
    '    at emitListeningNT (node:net:1983:10)\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:89:21)',
  context: 'index_creation',
  collection: 'dishes',
  timestamp: '2025-11-21 00:10:45:1045',
  level: 'error',
  service: 'butler-api',
  environment: 'development'
}
{
  message: "Cannot read properties of undefined (reading 'collection')",
  stack: "TypeError: Cannot read properties of undefined (reading 'collection')\n" +
    '    at createDatabaseIndexes (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:115:31)\n' +
    '    at initializeDatabaseOptimizations (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:359:11)\n' +
    '    at Server.<anonymous> (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/index.js:251:11)\n' +
    '    at Object.onceWrapper (node:events:632:28)\n' +
    '    at Server.emit (node:events:530:35)\n' +
    '    at emitListeningNT (node:net:1983:10)\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:89:21)',
  context: 'index_creation',
  collection: 'outlets',
  timestamp: '2025-11-21 00:10:45:1045',
  level: 'error',
  service: 'butler-api',
  environment: 'development'
}
{
  message: "Cannot read properties of undefined (reading 'collection')",
  stack: "TypeError: Cannot read properties of undefined (reading 'collection')\n" +
    '    at createDatabaseIndexes (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:115:31)\n' +
    '    at initializeDatabaseOptimizations (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:359:11)\n' +
    '    at Server.<anonymous> (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/index.js:251:11)\n' +
    '    at Object.onceWrapper (node:events:632:28)\n' +
    '    at Server.emit (node:events:530:35)\n' +
    '    at emitListeningNT (node:net:1983:10)\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:89:21)',
  context: 'index_creation',
  collection: 'carts',
  timestamp: '2025-11-21 00:10:45:1045',
  level: 'error',
  service: 'butler-api',
  environment: 'development'
}
{
  message: "Cannot read properties of undefined (reading 'collection')",
  stack: "TypeError: Cannot read properties of undefined (reading 'collection')\n" +
    '    at createDatabaseIndexes (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:115:31)\n' +
    '    at initializeDatabaseOptimizations (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:359:11)\n' +
    '    at Server.<anonymous> (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/index.js:251:11)\n' +
    '    at Object.onceWrapper (node:events:632:28)\n' +
    '    at Server.emit (node:events:530:35)\n' +
    '    at emitListeningNT (node:net:1983:10)\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:89:21)',
  context: 'index_creation',
  collection: 'payments',
  timestamp: '2025-11-21 00:10:45:1045',
  level: 'error',
  service: 'butler-api',
  environment: 'development'
}
{
  message: "Cannot read properties of undefined (reading 'collection')",
  stack: "TypeError: Cannot read properties of undefined (reading 'collection')\n" +
    '    at createDatabaseIndexes (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:115:31)\n' +
    '    at initializeDatabaseOptimizations (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:359:11)\n' +
    '    at Server.<anonymous> (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/index.js:251:11)\n' +
    '    at Object.onceWrapper (node:events:632:28)\n' +
    '    at Server.emit (node:events:530:35)\n' +
    '    at emitListeningNT (node:net:1983:10)\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:89:21)',
  context: 'index_creation',
  collection: 'notifications',
  timestamp: '2025-11-21 00:10:45:1045',
  level: 'error',
  service: 'butler-api',
  environment: 'development'
}
{
  message: "Cannot read properties of undefined (reading 'collection')",
  stack: "TypeError: Cannot read properties of undefined (reading 'collection')\n" +
    '    at createDatabaseIndexes (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:115:31)\n' +
    '    at initializeDatabaseOptimizations (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:359:11)\n' +
    '    at Server.<anonymous> (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/index.js:251:11)\n' +
    '    at Object.onceWrapper (node:events:632:28)\n' +
    '    at Server.emit (node:events:530:35)\n' +
    '    at emitListeningNT (node:net:1983:10)\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:89:21)',
  context: 'index_creation',
  collection: 'subscriptions',
  timestamp: '2025-11-21 00:10:45:1045',
  level: 'error',
  service: 'butler-api',
  environment: 'development'
}
{
  message: "Cannot read properties of undefined (reading 'collection')",
  stack: "TypeError: Cannot read properties of undefined (reading 'collection')\n" +
    '    at createDatabaseIndexes (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:115:31)\n' +
    '    at initializeDatabaseOptimizations (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:359:11)\n' +
    '    at Server.<anonymous> (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/index.js:251:11)\n' +
    '    at Object.onceWrapper (node:events:632:28)\n' +
    '    at Server.emit (node:events:530:35)\n' +
    '    at emitListeningNT (node:net:1983:10)\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:89:21)',
  context: 'index_creation',
  collection: 'customerfeedbacks',
  timestamp: '2025-11-21 00:10:45:1045',
  level: 'error',
  service: 'butler-api',
  environment: 'development'
}
{
  message: "Cannot read properties of undefined (reading 'collection')",
  stack: "TypeError: Cannot read properties of undefined (reading 'collection')\n" +
    '    at createDatabaseIndexes (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:115:31)\n' +
    '    at initializeDatabaseOptimizations (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:359:11)\n' +
    '    at Server.<anonymous> (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/index.js:251:11)\n' +
    '    at Object.onceWrapper (node:events:632:28)\n' +
    '    at Server.emit (node:events:530:35)\n' +
    '    at emitListeningNT (node:net:1983:10)\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:89:21)',
  context: 'index_creation',
  collection: 'conversations',
  timestamp: '2025-11-21 00:10:45:1045',
  level: 'error',
  service: 'butler-api',
  environment: 'development'
}
{
  message: "Cannot read properties of undefined (reading 'collection')",
  stack: "TypeError: Cannot read properties of undefined (reading 'collection')\n" +
    '    at createDatabaseIndexes (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:115:31)\n' +
    '    at initializeDatabaseOptimizations (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:359:11)\n' +
    '    at Server.<anonymous> (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/index.js:251:11)\n' +
    '    at Object.onceWrapper (node:events:632:28)\n' +
    '    at Server.emit (node:events:530:35)\n' +
    '    at emitListeningNT (node:net:1983:10)\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:89:21)',
  context: 'index_creation',
  collection: 'auditlogs',
  timestamp: '2025-11-21 00:10:45:1045',
  level: 'error',
  service: 'butler-api',
  environment: 'development'
}
{
  message: 'Database index creation completed',
  timestamp: '2025-11-21 00:10:45:1045',
  level: 'info',
  service: 'butler-api',
  environment: 'development'
}
{
  message: 'Database optimizations initialized successfully',
  timestamp: '2025-11-21 00:10:45:1045',
  level: 'info',
  service: 'butler-api',
  environment: 'development'
}
{
  message: 'Redis not configured, using in-memory cache fallback',
  timestamp: '2025-11-21 00:10:45:1045',
  level: 'info',
  service: 'butler-api',
  environment: 'development'
}
{
  message: '⚡ Performance optimizations initialized',
  timestamp: '2025-11-21 00:10:45:1045',
  level: 'info',
  service: 'butler-api',
  environment: 'development'
}
{
  message: '⏰ Cron jobs initialized',
  timestamp: '2025-11-21 00:10:45:1045',
  level: 'info',
  service: 'butler-api',
  environment: 'development'
}
{
  message: {
    method: 'POST',
    url: '/api/v1/user/test-order-validation',
    statusCode: 400,
    responseTime: '12ms',
    ip: '::1',
    userAgent: 'curl/8.7.1',
    userId: 'anonymous',
    contentLength: '150',
    timestamp: '2025-11-20T18:41:05.676Z'
  },
  level: 'http',
  timestamp: '2025-11-21 00:11:05:115'
}
{
  message: {
    method: 'POST',
    url: '/api/v1/user/test-order-validation',
    statusCode: 200,
    responseTime: '2ms',
    ip: '::1',
    userAgent: 'curl/8.7.1',
    userId: 'anonymous',
    contentLength: '47',
    timestamp: '2025-11-20T18:41:13.281Z'
  },
  level: 'http',
  timestamp: '2025-11-21 00:11:13:1113'
}
{
  message: {
    method: 'POST',
    url: '/api/v1/user/test-order-validation',
    statusCode: 400,
    responseTime: '1ms',
    ip: '::1',
    userAgent: 'curl/8.7.1',
    userId: 'anonymous',
    contentLength: '175',
    timestamp: '2025-11-20T18:41:20.683Z'
  },
  level: 'http',
  timestamp: '2025-11-21 00:11:20:1120'
}
{
  message: {
    method: 'POST',
    url: '/api/v1/user/test-order-validation',
    statusCode: 200,
    responseTime: '2ms',
    ip: '::1',
    userAgent: 'curl/8.7.1',
    userId: 'anonymous',
    contentLength: '47',
    timestamp: '2025-11-20T18:41:28.338Z'
  },
  level: 'http',
  timestamp: '2025-11-21 00:11:28:1128'
}
{
  message: {
    method: 'POST',
    url: '/api/v1/admin/login',
    statusCode: 200,
    responseTime: '109ms',
    ip: '::1',
    userAgent: 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
    userId: 'anonymous',
    contentLength: '515',
    timestamp: '2025-11-20T18:43:25.235Z'
  },
  level: 'http',
  timestamp: '2025-11-21 00:13:25:1325'
}
{
  message: {
    method: 'GET',
    url: '/api/v1/get-theme/6899f5527403b47ea1df69a2',
    statusCode: 200,
    responseTime: '39ms',
    ip: '::1',
    userAgent: 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
    userId: 'anonymous',
    contentLength: '194',
    timestamp: '2025-11-20T18:43:26.460Z'
  },
  level: 'http',
  timestamp: '2025-11-21 00:13:26:1326'
}
{
  message: {
    method: 'GET',
    url: '/api/v1/get-theme/6899f5527403b47ea1df69a2',
    statusCode: 304,
    responseTime: '27ms',
    ip: '::1',
    userAgent: 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
    userId: 'anonymous',
    contentLength: undefined,
    timestamp: '2025-11-20T18:43:26.489Z'
  },
  level: 'http',
  timestamp: '2025-11-21 00:13:26:1326'
}
{
  message: {
    method: 'GET',
    url: '/api/v1/admin/analytics?daysToAnalyze=30',
    statusCode: 200,
    responseTime: '243ms',
    ip: '::1',
    userAgent: 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
    userId: '6899f59a7403b47ea1df6b07',
    contentLength: undefined,
    timestamp: '2025-11-20T18:43:26.686Z'
  },
  level: 'http',
  timestamp: '2025-11-21 00:13:26:1326'
}
{
  message: {
    method: 'GET',
    url: '/api/v1/admin/analytics?daysToAnalyze=30',
    statusCode: 304,
    responseTime: '251ms',
    ip: '::1',
    userAgent: 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
    userId: '6899f59a7403b47ea1df6b07',
    contentLength: undefined,
    timestamp: '2025-11-20T18:43:26.938Z'
  },
  level: 'http',
  timestamp: '2025-11-21 00:13:26:1326'
}
{
  message: {
    method: 'GET',
    url: '/api/v1/admin/outlets',
    statusCode: 200,
    responseTime: '68ms',
    ip: '::1',
    userAgent: 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
    userId: '6899f59a7403b47ea1df6b07',
    contentLength: undefined,
    timestamp: '2025-11-20T18:43:32.882Z'
  },
  level: 'http',
  timestamp: '2025-11-21 00:13:32:1332'
}
{
  message: {
    method: 'GET',
    url: '/api/v1/admin/employees?page=1&limit=20&search=',
    statusCode: 200,
    responseTime: '101ms',
    ip: '::1',
    userAgent: 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
    userId: '6899f59a7403b47ea1df6b07',
    contentLength: '465',
    timestamp: '2025-11-20T18:43:32.921Z'
  },
  level: 'http',
  timestamp: '2025-11-21 00:13:32:1332'
}
{
  message: {
    method: 'GET',
    url: '/api/v1/admin/orders/691f608f6351e7fc770d7bfe/payment',
    statusCode: 404,
    responseTime: '49ms',
    ip: '::1',
    userAgent: 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
    userId: '6899f59a7403b47ea1df6b07',
    contentLength: '47',
    timestamp: '2025-11-20T18:43:32.975Z'
  },
  level: 'http',
  timestamp: '2025-11-21 00:13:32:1332'
}
{
  message: 'Performance: cache_miss',
  duration: '0ms',
  key: 'butler:dishes:categoryId:all|foodChainId:6899f5527403b47ea1df69a2|includeUnavailable:true|outletId:all|personalize:false',
  timestamp: '2025-11-21 00:13:32:1332',
  level: 'info',
  service: 'butler-api',
  environment: 'development'
}
{
  message: {
    method: 'GET',
    url: '/api/v1/admin/orders/691f60d16351e7fc770d7d43/payment',
    statusCode: 404,
    responseTime: '58ms',
    ip: '::1',
    userAgent: 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
    userId: '6899f59a7403b47ea1df6b07',
    contentLength: '47',
    timestamp: '2025-11-20T18:43:32.980Z'
  },
  level: 'http',
  timestamp: '2025-11-21 00:13:32:1332'
}
{
  message: {
    method: 'GET',
    url: '/api/v1/admin/outlets',
    statusCode: 304,
    responseTime: '98ms',
    ip: '::1',
    userAgent: 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
    userId: '6899f59a7403b47ea1df6b07',
    contentLength: undefined,
    timestamp: '2025-11-20T18:43:32.981Z'
  },
  level: 'http',
  timestamp: '2025-11-21 00:13:32:1332'
}
{
  message: {
    method: 'GET',
    url: '/api/v1/admin/orders/68dc302fce6dcce93175ec9e/payment',
    statusCode: 404,
    responseTime: '65ms',
    ip: '::1',
    userAgent: 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
    userId: '6899f59a7403b47ea1df6b07',
    contentLength: '47',
    timestamp: '2025-11-20T18:43:32.995Z'
  },
  level: 'http',
  timestamp: '2025-11-21 00:13:32:1332'
}
{
  message: {
    method: 'GET',
    url: '/api/v1/admin/orders/68cc371f4d9849d90f6a387f/payment',
    statusCode: 404,
    responseTime: '59ms',
    ip: '::1',
    userAgent: 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
    userId: '6899f59a7403b47ea1df6b07',
    contentLength: '47',
    timestamp: '2025-11-20T18:43:32.996Z'
  },
  level: 'http',
  timestamp: '2025-11-21 00:13:32:1332'
}
{
  message: {
    method: 'GET',
    url: '/api/v1/admin/orders/68cdb0ee01c6fb8974287c71/payment',
    statusCode: 404,
    responseTime: '53ms',
    ip: '::1',
    userAgent: 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
    userId: '6899f59a7403b47ea1df6b07',
    contentLength: '47',
    timestamp: '2025-11-20T18:43:33.030Z'
  },
  level: 'http',
  timestamp: '2025-11-21 00:13:33:1333'
}
{
  message: {
    method: 'GET',
    url: '/api/v1/admin/orders/68cad0864d9849d90f6a1a6f/payment',
    statusCode: 404,
    responseTime: '84ms',
    ip: '::1',
    userAgent: 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
    userId: '6899f59a7403b47ea1df6b07',
    contentLength: '47',
    timestamp: '2025-11-20T18:43:33.066Z'
  },
  level: 'http',
  timestamp: '2025-11-21 00:13:33:1333'
}
{
  message: {
    method: 'GET',
    url: '/api/v1/admin/orders/68dc3065ce6dcce93175ed0b/payment',
    statusCode: 404,
    responseTime: '85ms',
    ip: '::1',
    userAgent: 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
    userId: '6899f59a7403b47ea1df6b07',
    contentLength: '47',
    timestamp: '2025-11-20T18:43:33.066Z'
  },
  level: 'http',
  timestamp: '2025-11-21 00:13:33:1333'
}
{
  message: {
    method: 'GET',
    url: '/api/v1/admin/get-dishes?foodChainId=6899f5527403b47ea1df69a2&includeUnavailable=true',
    statusCode: 200,
    responseTime: '139ms',
    ip: '::1',
    userAgent: 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
    userId: '6899f59a7403b47ea1df6b07',
    contentLength: undefined,
    timestamp: '2025-11-20T18:43:33.072Z'
  },
  level: 'http',
  timestamp: '2025-11-21 00:13:33:1333'
}
{
  message: {
    method: 'GET',
    url: '/api/v1/admin/orders/68caea104d9849d90f6a2385/payment',
    statusCode: 404,
    responseTime: '77ms',
    ip: '::1',
    userAgent: 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
    userId: '6899f59a7403b47ea1df6b07',
    contentLength: '47',
    timestamp: '2025-11-20T18:43:33.111Z'
  },
  level: 'http',
  timestamp: '2025-11-21 00:13:33:1333'
}
{
  message: {
    method: 'GET',
    url: '/api/v1/admin/orders/68b1d4be3cbf6e1eae619d4c/payment',
    statusCode: 404,
    responseTime: '58ms',
    ip: '::1',
    userAgent: 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
    userId: '6899f59a7403b47ea1df6b07',
    contentLength: '47',
    timestamp: '2025-11-20T18:43:33.129Z'
  },
  level: 'http',
  timestamp: '2025-11-21 00:13:33:1333'
}
{
  message: {
    method: 'GET',
    url: '/api/v1/admin/orders/68b70d8beb253e6515800120/payment',
    statusCode: 404,
    responseTime: '61ms',
    ip: '::1',
    userAgent: 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
    userId: '6899f59a7403b47ea1df6b07',
    contentLength: '47',
    timestamp: '2025-11-20T18:43:33.131Z'
  },
  level: 'http',
  timestamp: '2025-11-21 00:13:33:1333'
}
{
  message: {
    method: 'GET',
    url: '/api/v1/admin/orders/68b1d49d3cbf6e1eae619d10/payment',
    statusCode: 404,
    responseTime: '66ms',
    ip: '::1',
    userAgent: 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
    userId: '6899f59a7403b47ea1df6b07',
    contentLength: '47',
    timestamp: '2025-11-20T18:43:33.138Z'
  },
  level: 'http',
  timestamp: '2025-11-21 00:13:33:1333'
}
{
  message: {
    method: 'GET',
    url: '/api/v1/admin/orders/68b1d33a3cbf6e1eae619b55/payment',
    statusCode: 404,
    responseTime: '46ms',
    ip: '::1',
    userAgent: 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
    userId: '6899f59a7403b47ea1df6b07',
    contentLength: '47',
    timestamp: '2025-11-20T18:43:33.159Z'
  },
  level: 'http',
  timestamp: '2025-11-21 00:13:33:1333'
}
{
  message: {
    method: 'GET',
    url: '/api/v1/admin/orders/68b1cfd9a080792e677e4336/payment',
    statusCode: 404,
    responseTime: '35ms',
    ip: '::1',
    userAgent: 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
    userId: '6899f59a7403b47ea1df6b07',
    contentLength: '47',
    timestamp: '2025-11-20T18:43:33.165Z'
  },
  level: 'http',
  timestamp: '2025-11-21 00:13:33:1333'
}
{
  message: {
    method: 'GET',
    url: '/api/v1/admin/outlets',
    statusCode: 304,
    responseTime: '43ms',
    ip: '::1',
    userAgent: 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
    userId: '6899f59a7403b47ea1df6b07',
    contentLength: undefined,
    timestamp: '2025-11-20T18:43:33.181Z'
  },
  level: 'http',
  timestamp: '2025-11-21 00:13:33:1333'
}
{
  message: 'Performance: cache_miss',
  duration: '0ms',
  key: 'butler:dishes:categoryId:all|foodChainId:6899f5527403b47ea1df69a2|includeUnavailable:true|outletId:all|personalize:false',
  timestamp: '2025-11-21 00:13:33:1333',
  level: 'info',
  service: 'butler-api',
  environment: 'development'
}
{
  message: {
    method: 'GET',
    url: '/api/v1/admin/employees?page=1&limit=20&search=',
    statusCode: 304,
    responseTime: '58ms',
    ip: '::1',
    userAgent: 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
    userId: '6899f59a7403b47ea1df6b07',
    contentLength: undefined,
    timestamp: '2025-11-20T18:43:33.190Z'
  },
  level: 'http',
  timestamp: '2025-11-21 00:13:33:1333'
}
{
  message: {
    method: 'GET',
    url: '/api/v1/admin/orders/68c7d03d12ed1fef2a6cb1b2/payment',
    statusCode: 200,
    responseTime: '245ms',
    ip: '::1',
    userAgent: 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
    userId: '6899f59a7403b47ea1df6b07',
    contentLength: undefined,
    timestamp: '2025-11-20T18:43:33.244Z'
  },
  level: 'http',
  timestamp: '2025-11-21 00:13:33:1333'
}
{
  message: {
    method: 'GET',
    url: '/api/v1/admin/employees?page=1&limit=20&search=',
    statusCode: 304,
    responseTime: '58ms',
    ip: '::1',
    userAgent: 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
    userId: '6899f59a7403b47ea1df6b07',
    contentLength: undefined,
    timestamp: '2025-11-20T18:43:33.248Z'
  },
  level: 'http',
  timestamp: '2025-11-21 00:13:33:1333'
}
{
  message: {
    method: 'GET',
    url: '/api/v1/admin/get-dishes?foodChainId=6899f5527403b47ea1df69a2&includeUnavailable=true',
    statusCode: 304,
    responseTime: '89ms',
    ip: '::1',
    userAgent: 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
    userId: '6899f59a7403b47ea1df6b07',
    contentLength: undefined,
    timestamp: '2025-11-20T18:43:33.249Z'
  },
  level: 'http',
  timestamp: '2025-11-21 00:13:33:1333'
}
{
  message: {
    method: 'GET',
    url: '/api/v1/admin/orders/68c7d7151655b54246de4221/payment',
    statusCode: 200,
    responseTime: '260ms',
    ip: '::1',
    userAgent: 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
    userId: '6899f59a7403b47ea1df6b07',
    contentLength: undefined,
    timestamp: '2025-11-20T18:43:33.258Z'
  },
  level: 'http',
  timestamp: '2025-11-21 00:13:33:1333'
}
{
  message: 'Performance: cache_miss',
  duration: '0ms',
  key: 'butler:dishes:categoryId:all|foodChainId:6899f5527403b47ea1df69a2|includeUnavailable:true|outletId:all|personalize:false',
  timestamp: '2025-11-21 00:13:33:1333',
  level: 'info',
  service: 'butler-api',
  environment: 'development'
}
{
  message: {
    method: 'GET',
    url: '/api/v1/admin/get-dishes?foodChainId=6899f5527403b47ea1df69a2&includeUnavailable=true',
    statusCode: 304,
    responseTime: '88ms',
    ip: '::1',
    userAgent: 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
    userId: '6899f59a7403b47ea1df6b07',
    contentLength: undefined,
    timestamp: '2025-11-20T18:43:33.338Z'
  },
  level: 'http',
  timestamp: '2025-11-21 00:13:33:1333'
}
{
  message: {
    method: 'GET',
    url: '/api/v1/admin/outlets',
    statusCode: 304,
    responseTime: '46ms',
    ip: '::1',
    userAgent: 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
    userId: '6899f59a7403b47ea1df6b07',
    contentLength: undefined,
    timestamp: '2025-11-20T18:43:35.554Z'
  },
  level: 'http',
  timestamp: '2025-11-21 00:13:35:1335'
}
{
  message: {
    method: 'GET',
    url: '/api/v1/admin/employees?page=1&limit=20&search=',
    statusCode: 304,
    responseTime: '65ms',
    ip: '::1',
    userAgent: 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
    userId: '6899f59a7403b47ea1df6b07',
    contentLength: undefined,
    timestamp: '2025-11-20T18:43:35.575Z'
  },
  level: 'http',
  timestamp: '2025-11-21 00:13:35:1335'
}
{
  message: {
    method: 'GET',
    url: '/api/v1/admin/outlets',
    statusCode: 304,
    responseTime: '40ms',
    ip: '::1',
    userAgent: 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
    userId: '6899f59a7403b47ea1df6b07',
    contentLength: undefined,
    timestamp: '2025-11-20T18:43:36.608Z'
  },
  level: 'http',
  timestamp: '2025-11-21 00:13:36:1336'
}
{
  message: {
    method: 'GET',
    url: '/api/v1/admin/customers-for-order?page=1&limit=100&search=&sortBy=createdAt&sortOrder=desc',
    statusCode: 200,
    responseTime: '63ms',
    ip: '::1',
    userAgent: 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
    userId: '6899f59a7403b47ea1df6b07',
    contentLength: undefined,
    timestamp: '2025-11-20T18:43:36.683Z'
  },
  level: 'http',
  timestamp: '2025-11-21 00:13:36:1336'
}
{
  message: {
    method: 'GET',
    url: '/api/v1/admin/customers-for-order?page=1&limit=10&search=va&sortBy=createdAt&sortOrder=desc',
    statusCode: 200,
    responseTime: '67ms',
    ip: '::1',
    userAgent: 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
    userId: '6899f59a7403b47ea1df6b07',
    contentLength: '885',
    timestamp: '2025-11-20T18:43:39.167Z'
  },
  level: 'http',
  timestamp: '2025-11-21 00:13:39:1339'
}
{
  message: {
    method: 'GET',
    url: '/api/v1/admin/customers-for-order?page=1&limit=10&search=vai&sortBy=createdAt&sortOrder=desc',
    statusCode: 200,
    responseTime: '66ms',
    ip: '::1',
    userAgent: 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
    userId: '6899f59a7403b47ea1df6b07',
    contentLength: '614',
    timestamp: '2025-11-20T18:43:39.398Z'
  },
  level: 'http',
  timestamp: '2025-11-21 00:13:39:1339'
}
{
  message: {
    method: 'GET',
    url: '/api/v1/admin/orders/68c7d03d12ed1fef2a6cb1b2/payment',
    statusCode: 304,
    responseTime: '171ms',
    ip: '::1',
    userAgent: 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
    userId: '6899f59a7403b47ea1df6b07',
    contentLength: undefined,
    timestamp: '2025-11-20T18:43:40.684Z'
  },
  level: 'http',
  timestamp: '2025-11-21 00:13:40:1340'
}
{
  message: {
    method: 'POST',
    url: '/api/v1/offers/applicable',
    statusCode: 200,
    responseTime: '28ms',
    ip: '::1',
    userAgent: 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
    userId: 'anonymous',
    contentLength: '36',
    timestamp: '2025-11-20T18:43:43.215Z'
  },
  level: 'http',
  timestamp: '2025-11-21 00:13:43:1343'
}
{
  message: {
    method: 'GET',
    url: '/api/v1/admin/orders/68c7d03d12ed1fef2a6cb1b2/payment',
    statusCode: 304,
    responseTime: '102ms',
    ip: '::1',
    userAgent: 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
    userId: '6899f59a7403b47ea1df6b07',
    contentLength: undefined,
    timestamp: '2025-11-20T18:43:45.612Z'
  },
  level: 'http',
  timestamp: '2025-11-21 00:13:45:1345'
}
{
  message: {
    method: 'GET',
    url: '/api/v1/admin/orders/68dc3065ce6dcce93175ed0b/payment',
    statusCode: 404,
    responseTime: '36ms',
    ip: '::1',
    userAgent: 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
    userId: '6899f59a7403b47ea1df6b07',
    contentLength: '47',
    timestamp: '2025-11-20T18:43:45.790Z'
  },
  level: 'http',
  timestamp: '2025-11-21 00:13:45:1345'
}
{
  message: {
    method: 'GET',
    url: '/api/v1/admin/orders/691f608f6351e7fc770d7bfe/payment',
    statusCode: 404,
    responseTime: '39ms',
    ip: '::1',
    userAgent: 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
    userId: '6899f59a7403b47ea1df6b07',
    contentLength: '47',
    timestamp: '2025-11-20T18:43:45.792Z'
  },
  level: 'http',
  timestamp: '2025-11-21 00:13:45:1345'
}
{
  message: {
    method: 'GET',
    url: '/api/v1/admin/orders/691f60d16351e7fc770d7d43/payment',
    statusCode: 404,
    responseTime: '43ms',
    ip: '::1',
    userAgent: 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
    userId: '6899f59a7403b47ea1df6b07',
    contentLength: '47',
    timestamp: '2025-11-20T18:43:45.793Z'
  },
  level: 'http',
  timestamp: '2025-11-21 00:13:45:1345'
}
{
  message: {
    method: 'GET',
    url: '/api/v1/admin/orders/68cdb0ee01c6fb8974287c71/payment',
    statusCode: 404,
    responseTime: '44ms',
    ip: '::1',
    userAgent: 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
    userId: '6899f59a7403b47ea1df6b07',
    contentLength: '47',
    timestamp: '2025-11-20T18:43:45.796Z'
  },
  level: 'http',
  timestamp: '2025-11-21 00:13:45:1345'
}
{
  message: {
    method: 'GET',
    url: '/api/v1/admin/orders/68dc302fce6dcce93175ec9e/payment',
    statusCode: 404,
    responseTime: '43ms',
    ip: '::1',
    userAgent: 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
    userId: '6899f59a7403b47ea1df6b07',
    contentLength: '47',
    timestamp: '2025-11-20T18:43:45.797Z'
  },
  level: 'http',
  timestamp: '2025-11-21 00:13:45:1345'
}
{
  message: {
    method: 'GET',
    url: '/api/v1/admin/orders/68cc371f4d9849d90f6a387f/payment',
    statusCode: 404,
    responseTime: '37ms',
    ip: '::1',
    userAgent: 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
    userId: '6899f59a7403b47ea1df6b07',
    contentLength: '47',
    timestamp: '2025-11-20T18:43:45.828Z'
  },
  level: 'http',
  timestamp: '2025-11-21 00:13:45:1345'
}
{
  message: {
    method: 'GET',
    url: '/api/v1/admin/orders/68caea104d9849d90f6a2385/payment',
    statusCode: 404,
    responseTime: '40ms',
    ip: '::1',
    userAgent: 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
    userId: '6899f59a7403b47ea1df6b07',
    contentLength: '47',
    timestamp: '2025-11-20T18:43:45.834Z'
  },
  level: 'http',
  timestamp: '2025-11-21 00:13:45:1345'
}
{
  message: {
    method: 'GET',
    url: '/api/v1/admin/orders/68cad0864d9849d90f6a1a6f/payment',
    statusCode: 404,
    responseTime: '40ms',
    ip: '::1',
    userAgent: 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
    userId: '6899f59a7403b47ea1df6b07',
    contentLength: '47',
    timestamp: '2025-11-20T18:43:45.835Z'
  },
  level: 'http',
  timestamp: '2025-11-21 00:13:45:1345'
}
{
  message: {
    method: 'GET',
    url: '/api/v1/admin/orders/68b70d8beb253e6515800120/payment',
    statusCode: 404,
    responseTime: '40ms',
    ip: '::1',
    userAgent: 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
    userId: '6899f59a7403b47ea1df6b07',
    contentLength: '47',
    timestamp: '2025-11-20T18:43:45.837Z'
  },
  level: 'http',
  timestamp: '2025-11-21 00:13:45:1345'
}
{
  message: {
    method: 'GET',
    url: '/api/v1/admin/orders/68b1d4be3cbf6e1eae619d4c/payment',
    statusCode: 404,
    responseTime: '45ms',
    ip: '::1',
    userAgent: 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
    userId: '6899f59a7403b47ea1df6b07',
    contentLength: '47',
    timestamp: '2025-11-20T18:43:45.843Z'
  },
  level: 'http',
  timestamp: '2025-11-21 00:13:45:1345'
}
{
  message: {
    method: 'GET',
    url: '/api/v1/admin/orders/68b1d49d3cbf6e1eae619d10/payment',
    statusCode: 404,
    responseTime: '36ms',
    ip: '::1',
    userAgent: 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
    userId: '6899f59a7403b47ea1df6b07',
    contentLength: '47',
    timestamp: '2025-11-20T18:43:45.865Z'
  },
  level: 'http',
  timestamp: '2025-11-21 00:13:45:1345'
}
{
  message: {
    method: 'GET',
    url: '/api/v1/admin/orders/68b1d33a3cbf6e1eae619b55/payment',
    statusCode: 404,
    responseTime: '40ms',
    ip: '::1',
    userAgent: 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
    userId: '6899f59a7403b47ea1df6b07',
    contentLength: '47',
    timestamp: '2025-11-20T18:43:45.875Z'
  },
  level: 'http',
  timestamp: '2025-11-21 00:13:45:1345'
}
{
  message: {
    method: 'GET',
    url: '/api/v1/admin/orders/68b1cfd9a080792e677e4336/payment',
    statusCode: 404,
    responseTime: '40ms',
    ip: '::1',
    userAgent: 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
    userId: '6899f59a7403b47ea1df6b07',
    contentLength: '47',
    timestamp: '2025-11-20T18:43:45.877Z'
  },
  level: 'http',
  timestamp: '2025-11-21 00:13:45:1345'
}
{
  message: {
    method: 'GET',
    url: '/api/v1/admin/orders/691f6161fb1621657e19e6ee/payment',
    statusCode: 404,
    responseTime: '39ms',
    ip: '::1',
    userAgent: 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
    userId: '6899f59a7403b47ea1df6b07',
    contentLength: '47',
    timestamp: '2025-11-20T18:43:45.906Z'
  },
  level: 'http',
  timestamp: '2025-11-21 00:13:45:1345'
}
{
  message: {
    method: 'GET',
    url: '/api/v1/admin/orders/68c7d7151655b54246de4221/payment',
    statusCode: 304,
    responseTime: '80ms',
    ip: '::1',
    userAgent: 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
    userId: '6899f59a7403b47ea1df6b07',
    contentLength: undefined,
    timestamp: '2025-11-20T18:43:45.919Z'
  },
  level: 'http',
  timestamp: '2025-11-21 00:13:45:1345'
}
{
  message: {
    method: 'GET',
    url: '/api/v1/admin/orders/68c7d03d12ed1fef2a6cb1b2/payment',
    statusCode: 304,
    responseTime: '129ms',
    ip: '::1',
    userAgent: 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
    userId: '6899f59a7403b47ea1df6b07',
    contentLength: undefined,
    timestamp: '2025-11-20T18:43:45.973Z'
  },
  level: 'http',
  timestamp: '2025-11-21 00:13:45:1345'
}
{
  message: 'Slow request detected',
  stack: 'Error: Slow request detected\n' +
    '    at ServerResponse.<anonymous> (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/middlewares/errorHandler.js:261:18)\n' +
    '    at ServerResponse.emit (node:events:530:35)\n' +
    '    at onFinish (node:_http_outgoing:1082:10)\n' +
    '    at callback (node:internal/streams/writable:766:21)\n' +
    '    at afterWrite (node:internal/streams/writable:710:5)\n' +
    '    at afterWriteTick (node:internal/streams/writable:696:10)\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:89:21)',
  url: '/api/v1/admin/orders/create',
  method: 'POST',
  duration: '2982ms',
  threshold: '2000ms',
  ip: '::1',
  userId: '6899f59a7403b47ea1df6b07',
  timestamp: '2025-11-21 00:13:48:1348',
  level: 'error',
  service: 'butler-api',
  environment: 'development'
}
{
  message: {
    method: 'POST',
    url: '/api/v1/admin/orders/create',
    statusCode: 201,
    responseTime: '2983ms',
    ip: '::1',
    userAgent: 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
    userId: '6899f59a7403b47ea1df6b07',
    contentLength: '828',
    timestamp: '2025-11-20T18:43:48.346Z'
  },
  level: 'http',
  timestamp: '2025-11-21 00:13:48:1348'
}
{
  message: {
    method: 'GET',
    url: '/api/v1/admin/orders/68c7d03d12ed1fef2a6cb1b2/payment',
    statusCode: 304,
    responseTime: '76ms',
    ip: '::1',
    userAgent: 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
    userId: '6899f59a7403b47ea1df6b07',
    contentLength: undefined,
    timestamp: '2025-11-20T18:43:50.832Z'
  },
  level: 'http',
  timestamp: '2025-11-21 00:13:50:1350'
}
{
  message: {
    method: 'GET',
    url: '/api/v1/admin/orders/68c7d03d12ed1fef2a6cb1b2/payment',
    statusCode: 304,
    responseTime: '86ms',
    ip: '::1',
    userAgent: 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
    userId: '6899f59a7403b47ea1df6b07',
    contentLength: undefined,
    timestamp: '2025-11-20T18:43:55.841Z'
  },
  level: 'http',
  timestamp: '2025-11-21 00:13:55:1355'
}
{
  message: {
    method: 'GET',
    url: '/api/v1/admin/orders/68c7d03d12ed1fef2a6cb1b2/payment',
    statusCode: 304,
    responseTime: '86ms',
    ip: '::1',
    userAgent: 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
    userId: '6899f59a7403b47ea1df6b07',
    contentLength: undefined,
    timestamp: '2025-11-20T18:44:00.839Z'
  },
  level: 'http',
  timestamp: '2025-11-21 00:14:00:140'
}
{
  message: {
    method: 'GET',
    url: '/api/v1/admin/orders/68c7d03d12ed1fef2a6cb1b2/payment',
    statusCode: 304,
    responseTime: '197ms',
    ip: '::1',
    userAgent: 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
    userId: '6899f59a7403b47ea1df6b07',
    contentLength: undefined,
    timestamp: '2025-11-20T18:44:05.951Z'
  },
  level: 'http',
  timestamp: '2025-11-21 00:14:05:145'
}
{
  message: {
    method: 'GET',
    url: '/api/v1/admin/orders/68c7d03d12ed1fef2a6cb1b2/payment',
    statusCode: 304,
    responseTime: '81ms',
    ip: '::1',
    userAgent: 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
    userId: '6899f59a7403b47ea1df6b07',
    contentLength: undefined,
    timestamp: '2025-11-20T18:44:10.835Z'
  },
  level: 'http',
  timestamp: '2025-11-21 00:14:10:1410'
}
{
  message: {
    method: 'GET',
    url: '/api/v1/admin/orders/68c7d03d12ed1fef2a6cb1b2/payment',
    statusCode: 304,
    responseTime: '72ms',
    ip: '::1',
    userAgent: 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
    userId: '6899f59a7403b47ea1df6b07',
    contentLength: undefined,
    timestamp: '2025-11-20T18:44:15.826Z'
  },
  level: 'http',
  timestamp: '2025-11-21 00:14:15:1415'
}
{
  message: {
    method: 'GET',
    url: '/api/v1/admin/orders/68c7d03d12ed1fef2a6cb1b2/payment',
    statusCode: 304,
    responseTime: '82ms',
    ip: '::1',
    userAgent: 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
    userId: '6899f59a7403b47ea1df6b07',
    contentLength: undefined,
    timestamp: '2025-11-20T18:44:20.835Z'
  },
  level: 'http',
  timestamp: '2025-11-21 00:14:20:1420'
}
{
  message: {
    method: 'GET',
    url: '/api/v1/admin/orders/68c7d03d12ed1fef2a6cb1b2/payment',
    statusCode: 304,
    responseTime: '75ms',
    ip: '::1',
    userAgent: 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
    userId: '6899f59a7403b47ea1df6b07',
    contentLength: undefined,
    timestamp: '2025-11-20T18:44:25.828Z'
  },
  level: 'http',
  timestamp: '2025-11-21 00:14:25:1425'
}
{
  message: {
    method: 'GET',
    url: '/api/v1/admin/orders/68c7d03d12ed1fef2a6cb1b2/payment',
    statusCode: 304,
    responseTime: '75ms',
    ip: '::1',
    userAgent: 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
    userId: '6899f59a7403b47ea1df6b07',
    contentLength: undefined,
    timestamp: '2025-11-20T18:44:30.829Z'
  },
  level: 'http',
  timestamp: '2025-11-21 00:14:30:1430'
}
{
  message: {
    method: 'GET',
    url: '/api/v1/admin/orders/68c7d03d12ed1fef2a6cb1b2/payment',
    statusCode: 304,
    responseTime: '80ms',
    ip: '::1',
    userAgent: 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
    userId: '6899f59a7403b47ea1df6b07',
    contentLength: undefined,
    timestamp: '2025-11-20T18:44:35.832Z'
  },
  level: 'http',
  timestamp: '2025-11-21 00:14:35:1435'
}
{
  message: {
    method: 'GET',
    url: '/api/v1/admin/orders/68c7d03d12ed1fef2a6cb1b2/payment',
    statusCode: 304,
    responseTime: '79ms',
    ip: '::1',
    userAgent: 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
    userId: '6899f59a7403b47ea1df6b07',
    contentLength: undefined,
    timestamp: '2025-11-20T18:44:40.831Z'
  },
  level: 'http',
  timestamp: '2025-11-21 00:14:40:1440'
}
{
  message: {
    method: 'GET',
    url: '/api/v1/admin/orders/68c7d03d12ed1fef2a6cb1b2/payment',
    statusCode: 304,
    responseTime: '125ms',
    ip: '::1',
    userAgent: 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
    userId: '6899f59a7403b47ea1df6b07',
    contentLength: undefined,
    timestamp: '2025-11-20T18:44:45.881Z'
  },
  level: 'http',
  timestamp: '2025-11-21 00:14:45:1445'
}
{
  message: {
    method: 'GET',
    url: '/api/v1/admin/orders/68c7d03d12ed1fef2a6cb1b2/payment',
    statusCode: 304,
    responseTime: '76ms',
    ip: '::1',
    userAgent: 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
    userId: '6899f59a7403b47ea1df6b07',
    contentLength: undefined,
    timestamp: '2025-11-20T18:44:50.828Z'
  },
  level: 'http',
  timestamp: '2025-11-21 00:14:50:1450'
}
{
  message: {
    method: 'GET',
    url: '/api/v1/admin/orders/68c7d03d12ed1fef2a6cb1b2/payment',
    statusCode: 304,
    responseTime: '110ms',
    ip: '::1',
    userAgent: 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
    userId: '6899f59a7403b47ea1df6b07',
    contentLength: undefined,
    timestamp: '2025-11-20T18:44:55.862Z'
  },
  level: 'http',
  timestamp: '2025-11-21 00:14:55:1455'
}
{
  message: {
    method: 'GET',
    url: '/api/v1/admin/orders/68c7d03d12ed1fef2a6cb1b2/payment',
    statusCode: 304,
    responseTime: '76ms',
    ip: '::1',
    userAgent: 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
    userId: '6899f59a7403b47ea1df6b07',
    contentLength: undefined,
    timestamp: '2025-11-20T18:45:00.829Z'
  },
  level: 'http',
  timestamp: '2025-11-21 00:15:00:150'
}
{
  message: {
    method: 'GET',
    url: '/api/v1/admin/orders/68c7d03d12ed1fef2a6cb1b2/payment',
    statusCode: 304,
    responseTime: '85ms',
    ip: '::1',
    userAgent: 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
    userId: '6899f59a7403b47ea1df6b07',
    contentLength: undefined,
    timestamp: '2025-11-20T18:45:05.836Z'
  },
  level: 'http',
  timestamp: '2025-11-21 00:15:05:155'
}
{
  message: {
    method: 'GET',
    url: '/api/v1/admin/orders/68c7d03d12ed1fef2a6cb1b2/payment',
    statusCode: 304,
    responseTime: '80ms',
    ip: '::1',
    userAgent: 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
    userId: '6899f59a7403b47ea1df6b07',
    contentLength: undefined,
    timestamp: '2025-11-20T18:45:10.832Z'
  },
  level: 'http',
  timestamp: '2025-11-21 00:15:10:1510'
}
{
  message: {
    method: 'GET',
    url: '/api/v1/admin/orders/68c7d03d12ed1fef2a6cb1b2/payment',
    statusCode: 304,
    responseTime: '78ms',
    ip: '::1',
    userAgent: 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
    userId: '6899f59a7403b47ea1df6b07',
    contentLength: undefined,
    timestamp: '2025-11-20T18:45:15.830Z'
  },
  level: 'http',
  timestamp: '2025-11-21 00:15:15:1515'
}
{
  message: {
    method: 'GET',
    url: '/api/v1/admin/orders/68c7d03d12ed1fef2a6cb1b2/payment',
    statusCode: 304,
    responseTime: '78ms',
    ip: '::1',
    userAgent: 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
    userId: '6899f59a7403b47ea1df6b07',
    contentLength: undefined,
    timestamp: '2025-11-20T18:45:20.831Z'
  },
  level: 'http',
  timestamp: '2025-11-21 00:15:20:1520'
}
{
  message: {
    method: 'GET',
    url: '/api/v1/admin/orders/68c7d03d12ed1fef2a6cb1b2/payment',
    statusCode: 304,
    responseTime: '78ms',
    ip: '::1',
    userAgent: 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
    userId: '6899f59a7403b47ea1df6b07',
    contentLength: undefined,
    timestamp: '2025-11-20T18:45:25.831Z'
  },
  level: 'http',
  timestamp: '2025-11-21 00:15:25:1525'
}
{
  message: {
    method: 'GET',
    url: '/api/v1/admin/orders/68c7d03d12ed1fef2a6cb1b2/payment',
    statusCode: 304,
    responseTime: '86ms',
    ip: '::1',
    userAgent: 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
    userId: '6899f59a7403b47ea1df6b07',
    contentLength: undefined,
    timestamp: '2025-11-20T18:45:30.839Z'
  },
  level: 'http',
  timestamp: '2025-11-21 00:15:30:1530'
}
{
  message: {
    method: 'GET',
    url: '/api/v1/admin/orders/68c7d03d12ed1fef2a6cb1b2/payment',
    statusCode: 304,
    responseTime: '83ms',
    ip: '::1',
    userAgent: 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
    userId: '6899f59a7403b47ea1df6b07',
    contentLength: undefined,
    timestamp: '2025-11-20T18:45:35.833Z'
  },
  level: 'http',
  timestamp: '2025-11-21 00:15:35:1535'
}
{
  message: {
    method: 'GET',
    url: '/api/v1/admin/orders/68c7d03d12ed1fef2a6cb1b2/payment',
    statusCode: 304,
    responseTime: '110ms',
    ip: '::1',
    userAgent: 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
    userId: '6899f59a7403b47ea1df6b07',
    contentLength: undefined,
    timestamp: '2025-11-20T18:45:40.859Z'
  },
  level: 'http',
  timestamp: '2025-11-21 00:15:40:1540'
}
{
  message: {
    method: 'GET',
    url: '/api/v1/admin/orders/68c7d03d12ed1fef2a6cb1b2/payment',
    statusCode: 304,
    responseTime: '80ms',
    ip: '::1',
    userAgent: 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
    userId: '6899f59a7403b47ea1df6b07',
    contentLength: undefined,
    timestamp: '2025-11-20T18:45:45.833Z'
  },
  level: 'http',
  timestamp: '2025-11-21 00:15:45:1545'
}
{
  message: {
    method: 'GET',
    url: '/api/v1/admin/orders/68c7d03d12ed1fef2a6cb1b2/payment',
    statusCode: 304,
    responseTime: '86ms',
    ip: '::1',
    userAgent: 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
    userId: '6899f59a7403b47ea1df6b07',
    contentLength: undefined,
    timestamp: '2025-11-20T18:45:50.838Z'
  },
  level: 'http',
  timestamp: '2025-11-21 00:15:50:1550'
}
{
  message: {
    method: 'GET',
    url: '/api/v1/admin/orders/68c7d03d12ed1fef2a6cb1b2/payment',
    statusCode: 304,
    responseTime: '79ms',
    ip: '::1',
    userAgent: 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
    userId: '6899f59a7403b47ea1df6b07',
    contentLength: undefined,
    timestamp: '2025-11-20T18:45:55.830Z'
  },
  level: 'http',
  timestamp: '2025-11-21 00:15:55:1555'
}
{
  message: {
    method: 'GET',
    url: '/api/v1/admin/orders/68c7d03d12ed1fef2a6cb1b2/payment',
    statusCode: 304,
    responseTime: '170ms',
    ip: '::1',
    userAgent: 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
    userId: '6899f59a7403b47ea1df6b07',
    contentLength: undefined,
    timestamp: '2025-11-20T18:46:01.501Z'
  },
  level: 'http',
  timestamp: '2025-11-21 00:16:01:161'
}
{
  message: {
    method: 'GET',
    url: '/api/v1/admin/orders/68c7d03d12ed1fef2a6cb1b2/payment',
    statusCode: 304,
    responseTime: '79ms',
    ip: '::1',
    userAgent: 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
    userId: '6899f59a7403b47ea1df6b07',
    contentLength: undefined,
    timestamp: '2025-11-20T18:46:06.408Z'
  },
  level: 'http',
  timestamp: '2025-11-21 00:16:06:166'
}
{
  message: {
    method: 'GET',
    url: '/api/v1/admin/orders/68c7d03d12ed1fef2a6cb1b2/payment',
    statusCode: 304,
    responseTime: '88ms',
    ip: '::1',
    userAgent: 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
    userId: '6899f59a7403b47ea1df6b07',
    contentLength: undefined,
    timestamp: '2025-11-20T18:46:11.418Z'
  },
  level: 'http',
  timestamp: '2025-11-21 00:16:11:1611'
}
{
  message: {
    method: 'GET',
    url: '/api/v1/admin/orders/68c7d03d12ed1fef2a6cb1b2/payment',
    statusCode: 304,
    responseTime: '117ms',
    ip: '::1',
    userAgent: 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
    userId: '6899f59a7403b47ea1df6b07',
    contentLength: undefined,
    timestamp: '2025-11-20T18:46:15.869Z'
  },
  level: 'http',
  timestamp: '2025-11-21 00:16:15:1615'
}
{
  message: {
    method: 'GET',
    url: '/api/v1/admin/orders/68c7d03d12ed1fef2a6cb1b2/payment',
    statusCode: 304,
    responseTime: '82ms',
    ip: '::1',
    userAgent: 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
    userId: '6899f59a7403b47ea1df6b07',
    contentLength: undefined,
    timestamp: '2025-11-20T18:46:20.834Z'
  },
  level: 'http',
  timestamp: '2025-11-21 00:16:20:1620'
}
{
  message: {
    method: 'GET',
    url: '/api/v1/admin/orders/68c7d03d12ed1fef2a6cb1b2/payment',
    statusCode: 304,
    responseTime: '89ms',
    ip: '::1',
    userAgent: 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
    userId: '6899f59a7403b47ea1df6b07',
    contentLength: undefined,
    timestamp: '2025-11-20T18:46:25.847Z'
  },
  level: 'http',
  timestamp: '2025-11-21 00:16:25:1625'
}
{
  message: {
    method: 'GET',
    url: '/api/v1/admin/orders/68c7d03d12ed1fef2a6cb1b2/payment',
    statusCode: 304,
    responseTime: '217ms',
    ip: '::1',
    userAgent: 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
    userId: '6899f59a7403b47ea1df6b07',
    contentLength: undefined,
    timestamp: '2025-11-20T18:46:31.549Z'
  },
  level: 'http',
  timestamp: '2025-11-21 00:16:31:1631'
}
{
  message: {
    method: 'GET',
    url: '/api/v1/admin/orders/68c7d03d12ed1fef2a6cb1b2/payment',
    statusCode: 304,
    responseTime: '110ms',
    ip: '::1',
    userAgent: 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
    userId: '6899f59a7403b47ea1df6b07',
    contentLength: undefined,
    timestamp: '2025-11-20T18:46:36.449Z'
  },
  level: 'http',
  timestamp: '2025-11-21 00:16:36:1636'
}
{
  message: {
    method: 'GET',
    url: '/api/v1/admin/orders/68c7d03d12ed1fef2a6cb1b2/payment',
    statusCode: 304,
    responseTime: '75ms',
    ip: '::1',
    userAgent: 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
    userId: '6899f59a7403b47ea1df6b07',
    contentLength: undefined,
    timestamp: '2025-11-20T18:46:41.403Z'
  },
  level: 'http',
  timestamp: '2025-11-21 00:16:41:1641'
}
{
  message: {
    method: 'GET',
    url: '/api/v1/admin/orders/68c7d03d12ed1fef2a6cb1b2/payment',
    statusCode: 304,
    responseTime: '111ms',
    ip: '::1',
    userAgent: 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
    userId: '6899f59a7403b47ea1df6b07',
    contentLength: undefined,
    timestamp: '2025-11-20T18:46:46.438Z'
  },
  level: 'http',
  timestamp: '2025-11-21 00:16:46:1646'
}
{
  message: {
    method: 'GET',
    url: '/api/v1/admin/orders/68c7d03d12ed1fef2a6cb1b2/payment',
    statusCode: 304,
    responseTime: '81ms',
    ip: '::1',
    userAgent: 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
    userId: '6899f59a7403b47ea1df6b07',
    contentLength: undefined,
    timestamp: '2025-11-20T18:46:51.410Z'
  },
  level: 'http',
  timestamp: '2025-11-21 00:16:51:1651'
}
{
  message: {
    method: 'GET',
    url: '/api/v1/admin/orders/68c7d03d12ed1fef2a6cb1b2/payment',
    statusCode: 304,
    responseTime: '89ms',
    ip: '::1',
    userAgent: 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
    userId: '6899f59a7403b47ea1df6b07',
    contentLength: undefined,
    timestamp: '2025-11-20T18:46:56.417Z'
  },
  level: 'http',
  timestamp: '2025-11-21 00:16:56:1656'
}
{
  message: {
    method: 'GET',
    url: '/api/v1/admin/orders/68c7d03d12ed1fef2a6cb1b2/payment',
    statusCode: 304,
    responseTime: '86ms',
    ip: '::1',
    userAgent: 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
    userId: '6899f59a7403b47ea1df6b07',
    contentLength: undefined,
    timestamp: '2025-11-20T18:47:01.414Z'
  },
  level: 'http',
  timestamp: '2025-11-21 00:17:01:171'
}
{
  message: {
    method: 'GET',
    url: '/api/v1/admin/orders/68c7d03d12ed1fef2a6cb1b2/payment',
    statusCode: 304,
    responseTime: '159ms',
    ip: '::1',
    userAgent: 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
    userId: '6899f59a7403b47ea1df6b07',
    contentLength: undefined,
    timestamp: '2025-11-20T18:47:06.488Z'
  },
  level: 'http',
  timestamp: '2025-11-21 00:17:06:176'
}
{
  message: {
    method: 'GET',
    url: '/api/v1/admin/orders/68c7d03d12ed1fef2a6cb1b2/payment',
    statusCode: 304,
    responseTime: '81ms',
    ip: '::1',
    userAgent: 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
    userId: '6899f59a7403b47ea1df6b07',
    contentLength: undefined,
    timestamp: '2025-11-20T18:47:11.412Z'
  },
  level: 'http',
  timestamp: '2025-11-21 00:17:11:1711'
}
{
  message: {
    method: 'GET',
    url: '/api/v1/admin/orders/68c7d03d12ed1fef2a6cb1b2/payment',
    statusCode: 304,
    responseTime: '82ms',
    ip: '::1',
    userAgent: 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
    userId: '6899f59a7403b47ea1df6b07',
    contentLength: undefined,
    timestamp: '2025-11-20T18:47:16.410Z'
  },
  level: 'http',
  timestamp: '2025-11-21 00:17:16:1716'
}
{
  message: {
    method: 'GET',
    url: '/api/v1/admin/orders/68c7d03d12ed1fef2a6cb1b2/payment',
    statusCode: 304,
    responseTime: '91ms',
    ip: '::1',
    userAgent: 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
    userId: '6899f59a7403b47ea1df6b07',
    contentLength: undefined,
    timestamp: '2025-11-20T18:47:21.420Z'
  },
  level: 'http',
  timestamp: '2025-11-21 00:17:21:1721'
}
{
  message: {
    method: 'GET',
    url: '/api/v1/admin/orders/68c7d03d12ed1fef2a6cb1b2/payment',
    statusCode: 304,
    responseTime: '81ms',
    ip: '::1',
    userAgent: 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
    userId: '6899f59a7403b47ea1df6b07',
    contentLength: undefined,
    timestamp: '2025-11-20T18:47:26.408Z'
  },
  level: 'http',
  timestamp: '2025-11-21 00:17:26:1726'
}
{
  message: {
    method: 'GET',
    url: '/api/v1/admin/orders/68c7d03d12ed1fef2a6cb1b2/payment',
    statusCode: 304,
    responseTime: '103ms',
    ip: '::1',
    userAgent: 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
    userId: '6899f59a7403b47ea1df6b07',
    contentLength: undefined,
    timestamp: '2025-11-20T18:47:31.432Z'
  },
  level: 'http',
  timestamp: '2025-11-21 00:17:31:1731'
}
{
  message: {
    method: 'GET',
    url: '/api/v1/user/get_all_conversations?userId=null',
    statusCode: 200,
    responseTime: '2ms',
    ip: '::1',
    userAgent: 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
    userId: 'anonymous',
    contentLength: '74',
    timestamp: '2025-11-20T18:47:31.996Z'
  },
  level: 'http',
  timestamp: '2025-11-21 00:17:31:1731'
}
{
  message: {
    method: 'GET',
    url: '/api/v1/admin/orders/68c7d03d12ed1fef2a6cb1b2/payment',
    statusCode: 304,
    responseTime: '87ms',
    ip: '::1',
    userAgent: 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
    userId: '6899f59a7403b47ea1df6b07',
    contentLength: undefined,
    timestamp: '2025-11-20T18:47:36.414Z'
  },
  level: 'http',
  timestamp: '2025-11-21 00:17:36:1736'
}
{
  message: {
    method: 'POST',
    url: '/api/v1/user/login',
    statusCode: 200,
    responseTime: '94ms',
    ip: '::1',
    userAgent: 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
    userId: 'anonymous',
    contentLength: '790',
    timestamp: '2025-11-20T18:47:41.119Z'
  },
  level: 'http',
  timestamp: '2025-11-21 00:17:41:1741'
}
{
  message: {
    method: 'GET',
    url: '/api/v1/user/get_all_conversations?userId=67d819a968f029236483e0e9',
    statusCode: 200,
    responseTime: '98ms',
    ip: '::1',
    userAgent: 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
    userId: 'anonymous',
    contentLength: undefined,
    timestamp: '2025-11-20T18:47:41.349Z'
  },
  level: 'http',
  timestamp: '2025-11-21 00:17:41:1741'
}
{
  message: {
    method: 'GET',
    url: '/api/v1/admin/orders/68c7d03d12ed1fef2a6cb1b2/payment',
    statusCode: 304,
    responseTime: '82ms',
    ip: '::1',
    userAgent: 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
    userId: '6899f59a7403b47ea1df6b07',
    contentLength: undefined,
    timestamp: '2025-11-20T18:47:41.414Z'
  },
  level: 'http',
  timestamp: '2025-11-21 00:17:41:1741'
}
{
  message: {
    method: 'GET',
    url: '/api/v1/admin/orders/68c7d03d12ed1fef2a6cb1b2/payment',
    statusCode: 304,
    responseTime: '84ms',
    ip: '::1',
    userAgent: 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
    userId: '6899f59a7403b47ea1df6b07',
    contentLength: undefined,
    timestamp: '2025-11-20T18:47:46.412Z'
  },
  level: 'http',
  timestamp: '2025-11-21 00:17:46:1746'
}
{
  message: {
    method: 'GET',
    url: '/api/v1/admin/orders/68c7d03d12ed1fef2a6cb1b2/payment',
    statusCode: 304,
    responseTime: '89ms',
    ip: '::1',
    userAgent: 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
    userId: '6899f59a7403b47ea1df6b07',
    contentLength: undefined,
    timestamp: '2025-11-20T18:47:51.417Z'
  },
  level: 'http',
  timestamp: '2025-11-21 00:17:51:1751'
}
{
  message: {
    method: 'GET',
    url: '/api/v1/admin/orders/68c7d03d12ed1fef2a6cb1b2/payment',
    statusCode: 304,
    responseTime: '82ms',
    ip: '::1',
    userAgent: 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
    userId: '6899f59a7403b47ea1df6b07',
    contentLength: undefined,
    timestamp: '2025-11-20T18:47:56.414Z'
  },
  level: 'http',
  timestamp: '2025-11-21 00:17:56:1756'
}
{
  message: {
    method: 'GET',
    url: '/api/v1/admin/orders/68c7d03d12ed1fef2a6cb1b2/payment',
    statusCode: 304,
    responseTime: '82ms',
    ip: '::1',
    userAgent: 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
    userId: '6899f59a7403b47ea1df6b07',
    contentLength: undefined,
    timestamp: '2025-11-20T18:48:01.410Z'
  },
  level: 'http',
  timestamp: '2025-11-21 00:18:01:181'
}
{
  message: {
    method: 'GET',
    url: '/api/v1/admin/orders/68c7d03d12ed1fef2a6cb1b2/payment',
    statusCode: 304,
    responseTime: '80ms',
    ip: '::1',
    userAgent: 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
    userId: '6899f59a7403b47ea1df6b07',
    contentLength: undefined,
    timestamp: '2025-11-20T18:48:06.408Z'
  },
  level: 'http',
  timestamp: '2025-11-21 00:18:06:186'
}
{
  message: {
    method: 'GET',
    url: '/api/v1/user/get_all_conversations?userId=67d819a968f029236483e0e9',
    statusCode: 304,
    responseTime: '89ms',
    ip: '::1',
    userAgent: 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
    userId: 'anonymous',
    contentLength: undefined,
    timestamp: '2025-11-20T18:48:11.415Z'
  },
  level: 'http',
  timestamp: '2025-11-21 00:18:11:1811'
}
{
  message: {
    method: 'GET',
    url: '/api/v1/admin/orders/68c7d03d12ed1fef2a6cb1b2/payment',
    statusCode: 304,
    responseTime: '89ms',
    ip: '::1',
    userAgent: 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
    userId: '6899f59a7403b47ea1df6b07',
    contentLength: undefined,
    timestamp: '2025-11-20T18:48:11.416Z'
  },
  level: 'http',
  timestamp: '2025-11-21 00:18:11:1811'
}
{
  message: {
    method: 'GET',
    url: '/api/v1/admin/orders/68c7d03d12ed1fef2a6cb1b2/payment',
    statusCode: 304,
    responseTime: '109ms',
    ip: '::1',
    userAgent: 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
    userId: '6899f59a7403b47ea1df6b07',
    contentLength: undefined,
    timestamp: '2025-11-20T18:48:16.438Z'
  },
  level: 'http',
  timestamp: '2025-11-21 00:18:16:1816'
}
{
  message: {
    method: 'GET',
    url: '/api/v1/admin/orders/68c7d03d12ed1fef2a6cb1b2/payment',
    statusCode: 304,
    responseTime: '95ms',
    ip: '::1',
    userAgent: 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
    userId: '6899f59a7403b47ea1df6b07',
    contentLength: undefined,
    timestamp: '2025-11-20T18:48:21.432Z'
  },
  level: 'http',
  timestamp: '2025-11-21 00:18:21:1821'
}
{
  message: {
    method: 'GET',
    url: '/api/v1/admin/orders/68c7d03d12ed1fef2a6cb1b2/payment',
    statusCode: 304,
    responseTime: '80ms',
    ip: '::1',
    userAgent: 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
    userId: '6899f59a7403b47ea1df6b07',
    contentLength: undefined,
    timestamp: '2025-11-20T18:48:26.420Z'
  },
  level: 'http',
  timestamp: '2025-11-21 00:18:26:1826'
}
{
  message: {
    method: 'GET',
    url: '/api/v1/admin/orders/68c7d03d12ed1fef2a6cb1b2/payment',
    statusCode: 304,
    responseTime: '84ms',
    ip: '::1',
    userAgent: 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
    userId: '6899f59a7403b47ea1df6b07',
    contentLength: undefined,
    timestamp: '2025-11-20T18:48:31.428Z'
  },
  level: 'http',
  timestamp: '2025-11-21 00:18:31:1831'
}
{
  message: {
    method: 'GET',
    url: '/api/v1/admin/orders/68c7d03d12ed1fef2a6cb1b2/payment',
    statusCode: 304,
    responseTime: '134ms',
    ip: '::1',
    userAgent: 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
    userId: '6899f59a7403b47ea1df6b07',
    contentLength: undefined,
    timestamp: '2025-11-20T18:48:36.449Z'
  },
  level: 'http',
  timestamp: '2025-11-21 00:18:36:1836'
}
{
  message: {
    method: 'GET',
    url: '/api/v1/user/get_all_conversations?userId=67d819a968f029236483e0e9',
    statusCode: 304,
    responseTime: '97ms',
    ip: '::1',
    userAgent: 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
    userId: 'anonymous',
    contentLength: undefined,
    timestamp: '2025-11-20T18:48:39.837Z'
  },
  level: 'http',
  timestamp: '2025-11-21 00:18:39:1839'
}
{
  message: {
    method: 'GET',
    url: '/api/v1/get-theme/6899f5527403b47ea1df69a2',
    statusCode: 200,
    responseTime: '20ms',
    ip: '::1',
    userAgent: 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
    userId: 'anonymous',
    contentLength: '194',
    timestamp: '2025-11-20T18:48:40.675Z'
  },
  level: 'http',
  timestamp: '2025-11-21 00:18:40:1840'
}
{
  message: {
    method: 'GET',
    url: '/api/v1/get-theme/6899f5527403b47ea1df69a2',
    statusCode: 304,
    responseTime: '22ms',
    ip: '::1',
    userAgent: 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
    userId: 'anonymous',
    contentLength: undefined,
    timestamp: '2025-11-20T18:48:40.701Z'
  },
  level: 'http',
  timestamp: '2025-11-21 00:18:40:1840'
}
{
  message: "Can't find /api/v1/user/personalized-menu?foodChainId=6899f5527403b47ea1df69a2&outletId=6899fa5358dbcc8f5ced8116 on this server!",
  stack: undefined,
  url: '/api/v1/user/personalized-menu?foodChainId=6899f5527403b47ea1df69a2&outletId=6899fa5358dbcc8f5ced8116',
  method: 'GET',
  ip: '::1',
  userAgent: 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
  userId: 'anonymous',
  timestamp: '2025-11-21 00:18:40:1840',
  level: 'error',
  service: 'butler-api',
  environment: 'development'
}
{
  message: {
    method: 'GET',
    url: '/api/v1/user/personalized-menu?foodChainId=6899f5527403b47ea1df69a2&outletId=6899fa5358dbcc8f5ced8116',
    statusCode: 404,
    responseTime: '2ms',
    ip: '::1',
    userAgent: 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
    userId: 'anonymous',
    contentLength: '197',
    timestamp: '2025-11-20T18:48:40.725Z'
  },
  level: 'http',
  timestamp: '2025-11-21 00:18:40:1840'
}
{
  message: "Can't find /api/v1/user/personalized-menu?foodChainId=6899f5527403b47ea1df69a2&outletId=6899fa5358dbcc8f5ced8116 on this server!",
  stack: undefined,
  url: '/api/v1/user/personalized-menu?foodChainId=6899f5527403b47ea1df69a2&outletId=6899fa5358dbcc8f5ced8116',
  method: 'GET',
  ip: '::1',
  userAgent: 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
  userId: 'anonymous',
  timestamp: '2025-11-21 00:18:40:1840',
  level: 'error',
  service: 'butler-api',
  environment: 'development'
}
{
  message: {
    method: 'GET',
    url: '/api/v1/user/personalized-menu?foodChainId=6899f5527403b47ea1df69a2&outletId=6899fa5358dbcc8f5ced8116',
    statusCode: 404,
    responseTime: '1ms',
    ip: '::1',
    userAgent: 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
    userId: 'anonymous',
    contentLength: '197',
    timestamp: '2025-11-20T18:48:40.727Z'
  },
  level: 'http',
  timestamp: '2025-11-21 00:18:40:1840'
}
{
  message: 'Performance: cache_miss',
  duration: '0ms',
  key: 'butler:dishes:categoryId:all|foodChainId:6899f5527403b47ea1df69a2|includeUnavailable:false|outletId:6899fa5358dbcc8f5ced8116|personalize:true',
  timestamp: '2025-11-21 00:18:40:1840',
  level: 'info',
  service: 'butler-api',
  environment: 'development'
}
{
  message: {
    method: 'GET',
    url: '/api/v1/user/cart?foodChainId=6899f5527403b47ea1df69a2&outletId=6899fa5358dbcc8f5ced8116',
    statusCode: 200,
    responseTime: '107ms',
    ip: '::1',
    userAgent: 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
    userId: '67d819a968f029236483e0e9',
    contentLength: '559',
    timestamp: '2025-11-20T18:48:40.754Z'
  },
  level: 'http',
  timestamp: '2025-11-21 00:18:40:1840'
}
{
  message: 'Performance: cache_set',
  duration: '1ms',
  key: 'butler:dishes:categoryId:all|foodChainId:6899f5527403b47ea1df69a2|includeUnavailable:false|outletId:6899fa5358dbcc8f5ced8116|personalize:true',
  ttl: 300,
  timestamp: '2025-11-21 00:18:40:1840',
  level: 'info',
  service: 'butler-api',
  environment: 'development'
}
{
  message: 'Performance: getDishes_database_query',
  duration: '109ms',
  dishCount: 40,
  personalized: true,
  timestamp: '2025-11-21 00:18:40:1840',
  level: 'info',
  service: 'butler-api',
  environment: 'development'
}
{
  message: {
    method: 'GET',
    url: '/api/v1/user/cart?foodChainId=6899f5527403b47ea1df69a2&outletId=6899fa5358dbcc8f5ced8116',
    statusCode: 304,
    responseTime: '86ms',
    ip: '::1',
    userAgent: 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
    userId: '67d819a968f029236483e0e9',
    contentLength: undefined,
    timestamp: '2025-11-20T18:48:40.841Z'
  },
  level: 'http',
  timestamp: '2025-11-21 00:18:40:1840'
}
{
  message: {
    method: 'GET',
    url: '/api/v1/user/dishes?foodChainId=6899f5527403b47ea1df69a2&outletId=6899fa5358dbcc8f5ced8116&personalize=true&userId=67d819a968f029236483e0e9',
    statusCode: 200,
    responseTime: '111ms',
    ip: '::1',
    userAgent: 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
    userId: 'anonymous',
    contentLength: undefined,
    timestamp: '2025-11-20T18:48:40.842Z'
  },
  level: 'http',
  timestamp: '2025-11-21 00:18:40:1840'
}
{
  message: 'Performance: cache_hit',
  duration: '0ms',
  key: 'butler:dishes:categoryId:all|foodChainId:6899f5527403b47ea1df69a2|includeUnavailable:false|outletId:6899fa5358dbcc8f5ced8116|personalize:true',
  timestamp: '2025-11-21 00:18:40:1840',
  level: 'info',
  service: 'butler-api',
  environment: 'development'
}
{
  message: 'Performance: getDishes_cache_hit',
  duration: '0ms',
  cacheKey: 'butler:dishes:categoryId:all|foodChainId:6899f5527403b47ea1df69a2|includeUnavailable:false|outletId:6899fa5358dbcc8f5ced8116|personalize:true',
  dishCount: 40,
  timestamp: '2025-11-21 00:18:40:1840',
  level: 'info',
  service: 'butler-api',
  environment: 'development'
}
{
  message: {
    method: 'GET',
    url: '/api/v1/user/dishes?foodChainId=6899f5527403b47ea1df69a2&outletId=6899fa5358dbcc8f5ced8116&personalize=true&userId=67d819a968f029236483e0e9',
    statusCode: 200,
    responseTime: '3ms',
    ip: '::1',
    userAgent: 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
    userId: 'anonymous',
    contentLength: undefined,
    timestamp: '2025-11-20T18:48:40.846Z'
  },
  level: 'http',
  timestamp: '2025-11-21 00:18:40:1840'
}
{
  message: {
    method: 'GET',
    url: '/api/v1/user/get_conversation?userId=67d819a968f029236483e0e9&outletId=6899fa5358dbcc8f5ced8116',
    statusCode: 200,
    responseTime: '19ms',
    ip: '::1',
    userAgent: 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
    userId: 'anonymous',
    contentLength: undefined,
    timestamp: '2025-11-20T18:48:40.868Z'
  },
  level: 'http',
  timestamp: '2025-11-21 00:18:40:1840'
}
{
  message: {
    method: 'GET',
    url: '/api/v1/user/get_conversation?userId=67d819a968f029236483e0e9&outletId=6899fa5358dbcc8f5ced8116',
    statusCode: 304,
    responseTime: '19ms',
    ip: '::1',
    userAgent: 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
    userId: 'anonymous',
    contentLength: undefined,
    timestamp: '2025-11-20T18:48:40.887Z'
  },
  level: 'http',
  timestamp: '2025-11-21 00:18:40:1840'
}
{
  message: {
    method: 'GET',
    url: '/api/v1/admin/orders/68c7d03d12ed1fef2a6cb1b2/payment',
    statusCode: 304,
    responseTime: '85ms',
    ip: '::1',
    userAgent: 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
    userId: '6899f59a7403b47ea1df6b07',
    contentLength: undefined,
    timestamp: '2025-11-20T18:48:41.431Z'
  },
  level: 'http',
  timestamp: '2025-11-21 00:18:41:1841'
}
{
  message: {
    method: 'GET',
    url: '/api/v1/user/cart?foodChainId=6899f5527403b47ea1df69a2&outletId=6899fa5358dbcc8f5ced8116',
    statusCode: 304,
    responseTime: '85ms',
    ip: '::1',
    userAgent: 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
    userId: '67d819a968f029236483e0e9',
    contentLength: undefined,
    timestamp: '2025-11-20T18:48:41.436Z'
  },
  level: 'http',
  timestamp: '2025-11-21 00:18:41:1841'
}
{
  message: {
    method: 'GET',
    url: '/api/v1/admin/orders/68c7d03d12ed1fef2a6cb1b2/payment',
    statusCode: 304,
    responseTime: '89ms',
    ip: '::1',
    userAgent: 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
    userId: '6899f59a7403b47ea1df6b07',
    contentLength: undefined,
    timestamp: '2025-11-20T18:48:46.438Z'
  },
  level: 'http',
  timestamp: '2025-11-21 00:18:46:1846'
}
{
  message: {
    method: 'GET',
    url: '/api/v1/user/cart?foodChainId=6899f5527403b47ea1df69a2&outletId=6899fa5358dbcc8f5ced8116',
    statusCode: 304,
    responseTime: '90ms',
    ip: '::1',
    userAgent: 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
    userId: '67d819a968f029236483e0e9',
    contentLength: undefined,
    timestamp: '2025-11-20T18:48:49.064Z'
  },
  level: 'http',
  timestamp: '2025-11-21 00:18:49:1849'
}
{
  message: {
    method: 'GET',
    url: '/api/v1/user/cart?foodChainId=6899f5527403b47ea1df69a2&outletId=6899fa5358dbcc8f5ced8116',
    statusCode: 304,
    responseTime: '87ms',
    ip: '::1',
    userAgent: 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
    userId: '67d819a968f029236483e0e9',
    contentLength: undefined,
    timestamp: '2025-11-20T18:48:49.152Z'
  },
  level: 'http',
  timestamp: '2025-11-21 00:18:49:1849'
}
{
  message: {
    method: 'GET',
    url: '/api/v1/user/cart?foodChainId=6899f5527403b47ea1df69a2&outletId=6899fa5358dbcc8f5ced8116',
    statusCode: 304,
    responseTime: '85ms',
    ip: '::1',
    userAgent: 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
    userId: '67d819a968f029236483e0e9',
    contentLength: undefined,
    timestamp: '2025-11-20T18:48:49.731Z'
  },
  level: 'http',
  timestamp: '2025-11-21 00:18:49:1849'
}
{
  message: {
    method: 'GET',
    url: '/api/v1/admin/orders/68c7d03d12ed1fef2a6cb1b2/payment',
    statusCode: 304,
    responseTime: '95ms',
    ip: '::1',
    userAgent: 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
    userId: '6899f59a7403b47ea1df6b07',
    contentLength: undefined,
    timestamp: '2025-11-20T18:48:51.446Z'
  },
  level: 'http',
  timestamp: '2025-11-21 00:18:51:1851'
}
{
  message: {
    method: 'POST',
    url: '/api/v1/user/create-order',
    statusCode: 201,
    responseTime: '173ms',
    ip: '::1',
    userAgent: 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
    userId: '67d819a968f029236483e0e9',
    contentLength: '800',
    timestamp: '2025-11-20T18:48:52.633Z'
  },
  level: 'http',
  timestamp: '2025-11-21 00:18:52:1852'
}
{
  message: {
    method: 'DELETE',
    url: '/api/v1/user/cart/clear?foodChainId=6899f5527403b47ea1df69a2&outletId=6899fa5358dbcc8f5ced8116',
    statusCode: 200,
    responseTime: '69ms',
    ip: '::1',
    userAgent: 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
    userId: '67d819a968f029236483e0e9',
    contentLength: '292',
    timestamp: '2025-11-20T18:48:52.713Z'
  },
  level: 'http',
  timestamp: '2025-11-21 00:18:52:1852'
}
{
  message: {
    method: 'GET',
    url: '/api/v1/admin/orders/691f6161fb1621657e19e6ee/payment',
    statusCode: 404,
    responseTime: '45ms',
    ip: '::1',
    userAgent: 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
    userId: '6899f59a7403b47ea1df6b07',
    contentLength: '47',
    timestamp: '2025-11-20T18:48:53.068Z'
  },
  level: 'http',
  timestamp: '2025-11-21 00:18:53:1853'
}
{
  message: {
    method: 'GET',
    url: '/api/v1/admin/orders/691f608f6351e7fc770d7bfe/payment',
    statusCode: 404,
    responseTime: '40ms',
    ip: '::1',
    userAgent: 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
    userId: '6899f59a7403b47ea1df6b07',
    contentLength: '47',
    timestamp: '2025-11-20T18:48:53.071Z'
  },
  level: 'http',
  timestamp: '2025-11-21 00:18:53:1853'
}
{
  message: {
    method: 'GET',
    url: '/api/v1/admin/orders/691f60d16351e7fc770d7d43/payment',
    statusCode: 404,
    responseTime: '44ms',
    ip: '::1',
    userAgent: 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
    userId: '6899f59a7403b47ea1df6b07',
    contentLength: '47',
    timestamp: '2025-11-20T18:48:53.073Z'
  },
  level: 'http',
  timestamp: '2025-11-21 00:18:53:1853'
}
{
  message: {
    method: 'GET',
    url: '/api/v1/admin/orders/68dc302fce6dcce93175ec9e/payment',
    statusCode: 404,
    responseTime: '42ms',
    ip: '::1',
    userAgent: 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
    userId: '6899f59a7403b47ea1df6b07',
    contentLength: '47',
    timestamp: '2025-11-20T18:48:53.075Z'
  },
  level: 'http',
  timestamp: '2025-11-21 00:18:53:1853'
}
{
  message: {
    method: 'GET',
    url: '/api/v1/admin/orders/68cdb0ee01c6fb8974287c71/payment',
    statusCode: 404,
    responseTime: '50ms',
    ip: '::1',
    userAgent: 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
    userId: '6899f59a7403b47ea1df6b07',
    contentLength: '47',
    timestamp: '2025-11-20T18:48:53.077Z'
  },
  level: 'http',
  timestamp: '2025-11-21 00:18:53:1853'
}
{
  message: {
    method: 'GET',
    url: '/api/v1/admin/orders/68dc3065ce6dcce93175ed0b/payment',
    statusCode: 404,
    responseTime: '48ms',
    ip: '::1',
    userAgent: 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
    userId: '6899f59a7403b47ea1df6b07',
    contentLength: '47',
    timestamp: '2025-11-20T18:48:53.080Z'
  },
  level: 'http',
  timestamp: '2025-11-21 00:18:53:1853'
}
{
  message: {
    method: 'GET',
    url: '/api/v1/admin/orders/68cc371f4d9849d90f6a387f/payment',
    statusCode: 404,
    responseTime: '60ms',
    ip: '::1',
    userAgent: 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
    userId: '6899f59a7403b47ea1df6b07',
    contentLength: '47',
    timestamp: '2025-11-20T18:48:53.141Z'
  },
  level: 'http',
  timestamp: '2025-11-21 00:18:53:1853'
}
{
  message: {
    method: 'GET',
    url: '/api/v1/admin/orders/68cad0864d9849d90f6a1a6f/payment',
    statusCode: 404,
    responseTime: '53ms',
    ip: '::1',
    userAgent: 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
    userId: '6899f59a7403b47ea1df6b07',
    contentLength: '47',
    timestamp: '2025-11-20T18:48:53.142Z'
  },
  level: 'http',
  timestamp: '2025-11-21 00:18:53:1853'
}
{
  message: {
    method: 'GET',
    url: '/api/v1/admin/orders/68caea104d9849d90f6a2385/payment',
    statusCode: 404,
    responseTime: '63ms',
    ip: '::1',
    userAgent: 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
    userId: '6899f59a7403b47ea1df6b07',
    contentLength: '47',
    timestamp: '2025-11-20T18:48:53.147Z'
  },
  level: 'http',
  timestamp: '2025-11-21 00:18:53:1853'
}
{
  message: {
    method: 'GET',
    url: '/api/v1/admin/orders/68b70d8beb253e6515800120/payment',
    statusCode: 404,
    responseTime: '48ms',
    ip: '::1',
    userAgent: 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
    userId: '6899f59a7403b47ea1df6b07',
    contentLength: '47',
    timestamp: '2025-11-20T18:48:53.148Z'
  },
  level: 'http',
  timestamp: '2025-11-21 00:18:53:1853'
}
{
  message: {
    method: 'GET',
    url: '/api/v1/admin/orders/691f6294fb1621657e19ea1f/payment',
    statusCode: 404,
    responseTime: '56ms',
    ip: '::1',
    userAgent: 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
    userId: '6899f59a7403b47ea1df6b07',
    contentLength: '47',
    timestamp: '2025-11-20T18:48:53.149Z'
  },
  level: 'http',
  timestamp: '2025-11-21 00:18:53:1853'
}
{
  message: {
    method: 'GET',
    url: '/api/v1/admin/orders/68b1d4be3cbf6e1eae619d4c/payment',
    statusCode: 404,
    responseTime: '35ms',
    ip: '::1',
    userAgent: 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
    userId: '6899f59a7403b47ea1df6b07',
    contentLength: '47',
    timestamp: '2025-11-20T18:48:53.185Z'
  },
  level: 'http',
  timestamp: '2025-11-21 00:18:53:1853'
}
{
  message: {
    method: 'GET',
    url: '/api/v1/admin/orders/68b1d33a3cbf6e1eae619b55/payment',
    statusCode: 404,
    responseTime: '35ms',
    ip: '::1',
    userAgent: 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
    userId: '6899f59a7403b47ea1df6b07',
    contentLength: '47',
    timestamp: '2025-11-20T18:48:53.187Z'
  },
  level: 'http',
  timestamp: '2025-11-21 00:18:53:1853'
}
{
  message: {
    method: 'GET',
    url: '/api/v1/admin/orders/68b1cfd9a080792e677e4336/payment',
    statusCode: 404,
    responseTime: '39ms',
    ip: '::1',
    userAgent: 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
    userId: '6899f59a7403b47ea1df6b07',
    contentLength: '47',
    timestamp: '2025-11-20T18:48:53.191Z'
  },
  level: 'http',
  timestamp: '2025-11-21 00:18:53:1853'
}
{
  message: {
    method: 'GET',
    url: '/api/v1/admin/orders/68b1d49d3cbf6e1eae619d10/payment',
    statusCode: 404,
    responseTime: '41ms',
    ip: '::1',
    userAgent: 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
    userId: '6899f59a7403b47ea1df6b07',
    contentLength: '47',
    timestamp: '2025-11-20T18:48:53.192Z'
  },
  level: 'http',
  timestamp: '2025-11-21 00:18:53:1853'
}
{
  message: {
    method: 'GET',
    url: '/api/v1/admin/orders/68c7d03d12ed1fef2a6cb1b2/payment',
    statusCode: 304,
    responseTime: '94ms',
    ip: '::1',
    userAgent: 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
    userId: '6899f59a7403b47ea1df6b07',
    contentLength: undefined,
    timestamp: '2025-11-20T18:48:53.221Z'
  },
  level: 'http',
  timestamp: '2025-11-21 00:18:53:1853'
}
{
  message: {
    method: 'GET',
    url: '/api/v1/admin/orders/68c7d7151655b54246de4221/payment',
    statusCode: 304,
    responseTime: '177ms',
    ip: '::1',
    userAgent: 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
    userId: '6899f59a7403b47ea1df6b07',
    contentLength: undefined,
    timestamp: '2025-11-20T18:48:53.326Z'
  },
  level: 'http',
  timestamp: '2025-11-21 00:18:53:1853'
}
{
  message: {
    method: 'GET',
    url: '/api/v1/user/orders/691f6294fb1621657e19ea1f',
    statusCode: 200,
    responseTime: '64ms',
    ip: '::1',
    userAgent: 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
    userId: '67d819a968f029236483e0e9',
    contentLength: '842',
    timestamp: '2025-11-20T18:48:54.140Z'
  },
  level: 'http',
  timestamp: '2025-11-21 00:18:54:1854'
}
{
  message: {
    method: 'GET',
    url: '/api/v1/user/orders/691f6294fb1621657e19ea1f',
    statusCode: 304,
    responseTime: '64ms',
    ip: '::1',
    userAgent: 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
    userId: '67d819a968f029236483e0e9',
    contentLength: undefined,
    timestamp: '2025-11-20T18:48:54.204Z'
  },
  level: 'http',
  timestamp: '2025-11-21 00:18:54:1854'
}
{
  message: {
    method: 'GET',
    url: '/api/v1/admin/orders/68c7d03d12ed1fef2a6cb1b2/payment',
    statusCode: 304,
    responseTime: '90ms',
    ip: '::1',
    userAgent: 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
    userId: '6899f59a7403b47ea1df6b07',
    contentLength: undefined,
    timestamp: '2025-11-20T18:48:58.124Z'
  },
  level: 'http',
  timestamp: '2025-11-21 00:18:58:1858'
}
{
  message: {
    method: 'GET',
    url: '/api/v1/admin/orders/68c7d03d12ed1fef2a6cb1b2/payment',
    statusCode: 304,
    responseTime: '96ms',
    ip: '::1',
    userAgent: 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
    userId: '6899f59a7403b47ea1df6b07',
    contentLength: undefined,
    timestamp: '2025-11-20T18:49:03.129Z'
  },
  level: 'http',
  timestamp: '2025-11-21 00:19:03:193'
}
{
  message: {
    method: 'GET',
    url: '/api/v1/admin/orders/68c7d03d12ed1fef2a6cb1b2/payment',
    statusCode: 304,
    responseTime: '78ms',
    ip: '::1',
    userAgent: 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
    userId: '6899f59a7403b47ea1df6b07',
    contentLength: undefined,
    timestamp: '2025-11-20T18:49:08.111Z'
  },
  level: 'http',
  timestamp: '2025-11-21 00:19:08:198'
}
{
  message: {
    method: 'GET',
    url: '/api/v1/admin/orders/68c7d03d12ed1fef2a6cb1b2/payment',
    statusCode: 304,
    responseTime: '88ms',
    ip: '::1',
    userAgent: 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
    userId: '6899f59a7403b47ea1df6b07',
    contentLength: undefined,
    timestamp: '2025-11-20T18:49:13.125Z'
  },
  level: 'http',
  timestamp: '2025-11-21 00:19:13:1913'
}
{
  message: {
    method: 'GET',
    url: '/api/v1/admin/orders/68c7d03d12ed1fef2a6cb1b2/payment',
    statusCode: 304,
    responseTime: '79ms',
    ip: '::1',
    userAgent: 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
    userId: '6899f59a7403b47ea1df6b07',
    contentLength: undefined,
    timestamp: '2025-11-20T18:49:18.112Z'
  },
  level: 'http',
  timestamp: '2025-11-21 00:19:18:1918'
}
{
  message: {
    method: 'GET',
    url: '/api/v1/admin/orders/68c7d03d12ed1fef2a6cb1b2/payment',
    statusCode: 304,
    responseTime: '91ms',
    ip: '::1',
    userAgent: 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
    userId: '6899f59a7403b47ea1df6b07',
    contentLength: undefined,
    timestamp: '2025-11-20T18:49:23.123Z'
  },
  level: 'http',
  timestamp: '2025-11-21 00:19:23:1923'
}
{
  message: {
    method: 'GET',
    url: '/api/v1/admin/orders/68c7d03d12ed1fef2a6cb1b2/payment',
    statusCode: 304,
    responseTime: '162ms',
    ip: '::1',
    userAgent: 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
    userId: '6899f59a7403b47ea1df6b07',
    contentLength: undefined,
    timestamp: '2025-11-20T18:49:28.511Z'
  },
  level: 'http',
  timestamp: '2025-11-21 00:19:28:1928'
}
{
  message: {
    method: 'GET',
    url: '/api/v1/admin/orders/68c7d03d12ed1fef2a6cb1b2/payment',
    statusCode: 304,
    responseTime: '96ms',
    ip: '::1',
    userAgent: 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
    userId: '6899f59a7403b47ea1df6b07',
    contentLength: undefined,
    timestamp: '2025-11-20T18:49:33.446Z'
  },
  level: 'http',
  timestamp: '2025-11-21 00:19:33:1933'
}
{
  message: {
    method: 'GET',
    url: '/api/v1/admin/orders/68c7d03d12ed1fef2a6cb1b2/payment',
    statusCode: 304,
    responseTime: '79ms',
    ip: '::1',
    userAgent: 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
    userId: '6899f59a7403b47ea1df6b07',
    contentLength: undefined,
    timestamp: '2025-11-20T18:49:38.431Z'
  },
  level: 'http',
  timestamp: '2025-11-21 00:19:38:1938'
}
{
  message: {
    method: 'GET',
    url: '/api/v1/admin/orders/68c7d03d12ed1fef2a6cb1b2/payment',
    statusCode: 304,
    responseTime: '86ms',
    ip: '::1',
    userAgent: 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
    userId: '6899f59a7403b47ea1df6b07',
    contentLength: undefined,
    timestamp: '2025-11-20T18:49:43.436Z'
  },
  level: 'http',
  timestamp: '2025-11-21 00:19:43:1943'
}
{
  message: {
    method: 'GET',
    url: '/api/v1/admin/orders/68c7d03d12ed1fef2a6cb1b2/payment',
    statusCode: 304,
    responseTime: '77ms',
    ip: '::1',
    userAgent: 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
    userId: '6899f59a7403b47ea1df6b07',
    contentLength: undefined,
    timestamp: '2025-11-20T18:49:48.428Z'
  },
  level: 'http',
  timestamp: '2025-11-21 00:19:48:1948'
}
{
  message: {
    method: 'GET',
    url: '/api/v1/admin/orders/68c7d03d12ed1fef2a6cb1b2/payment',
    statusCode: 304,
    responseTime: '96ms',
    ip: '::1',
    userAgent: 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
    userId: '6899f59a7403b47ea1df6b07',
    contentLength: undefined,
    timestamp: '2025-11-20T18:49:53.136Z'
  },
  level: 'http',
  timestamp: '2025-11-21 00:19:53:1953'
}
{
  message: {
    method: 'GET',
    url: '/api/v1/admin/orders/68c7d03d12ed1fef2a6cb1b2/payment',
    statusCode: 304,
    responseTime: '225ms',
    ip: '::1',
    userAgent: 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
    userId: '6899f59a7403b47ea1df6b07',
    contentLength: undefined,
    timestamp: '2025-11-20T18:49:58.578Z'
  },
  level: 'http',
  timestamp: '2025-11-21 00:19:58:1958'
}
{
  message: {
    method: 'GET',
    url: '/api/v1/admin/orders/68c7d03d12ed1fef2a6cb1b2/payment',
    statusCode: 200,
    responseTime: '113ms',
    ip: '::1',
    userAgent: 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
    userId: '6899f59a7403b47ea1df6b07',
    contentLength: undefined,
    timestamp: '2025-11-20T18:50:03.465Z'
  },
  level: 'http',
  timestamp: '2025-11-21 00:20:03:203'
}
{
  message: {
    method: 'GET',
    url: '/api/v1/admin/orders/68c7d03d12ed1fef2a6cb1b2/payment',
    statusCode: 304,
    responseTime: '80ms',
    ip: '::1',
    userAgent: 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
    userId: '6899f59a7403b47ea1df6b07',
    contentLength: undefined,
    timestamp: '2025-11-20T18:50:08.431Z'
  },
  level: 'http',
  timestamp: '2025-11-21 00:20:08:208'
}
{
  message: {
    method: 'GET',
    url: '/api/v1/admin/orders/68c7d03d12ed1fef2a6cb1b2/payment',
    statusCode: 304,
    responseTime: '152ms',
    ip: '::1',
    userAgent: 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
    userId: '6899f59a7403b47ea1df6b07',
    contentLength: undefined,
    timestamp: '2025-11-20T18:50:13.502Z'
  },
  level: 'http',
  timestamp: '2025-11-21 00:20:13:2013'
}
{
  message: {
    method: 'GET',
    url: '/api/v1/admin/orders/68c7d03d12ed1fef2a6cb1b2/payment',
    statusCode: 304,
    responseTime: '102ms',
    ip: '::1',
    userAgent: 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
    userId: '6899f59a7403b47ea1df6b07',
    contentLength: undefined,
    timestamp: '2025-11-20T18:50:18.456Z'
  },
  level: 'http',
  timestamp: '2025-11-21 00:20:18:2018'
}
{
  message: {
    method: 'GET',
    url: '/api/v1/admin/orders/68c7d03d12ed1fef2a6cb1b2/payment',
    statusCode: 304,
    responseTime: '80ms',
    ip: '::1',
    userAgent: 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
    userId: '6899f59a7403b47ea1df6b07',
    contentLength: undefined,
    timestamp: '2025-11-20T18:50:23.445Z'
  },
  level: 'http',
  timestamp: '2025-11-21 00:20:23:2023'
}
{
  message: {
    method: 'GET',
    url: '/api/v1/admin/orders/68c7d03d12ed1fef2a6cb1b2/payment',
    statusCode: 304,
    responseTime: '88ms',
    ip: '::1',
    userAgent: 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
    userId: '6899f59a7403b47ea1df6b07',
    contentLength: undefined,
    timestamp: '2025-11-20T18:50:28.460Z'
  },
  level: 'http',
  timestamp: '2025-11-21 00:20:28:2028'
}
{
  message: {
    method: 'GET',
    url: '/api/v1/admin/orders/68c7d03d12ed1fef2a6cb1b2/payment',
    statusCode: 304,
    responseTime: '81ms',
    ip: '::1',
    userAgent: 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
    userId: '6899f59a7403b47ea1df6b07',
    contentLength: undefined,
    timestamp: '2025-11-20T18:50:33.432Z'
  },
  level: 'http',
  timestamp: '2025-11-21 00:20:33:2033'
}
{
  message: {
    method: 'GET',
    url: '/api/v1/admin/orders/68c7d03d12ed1fef2a6cb1b2/payment',
    statusCode: 304,
    responseTime: '88ms',
    ip: '::1',
    userAgent: 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
    userId: '6899f59a7403b47ea1df6b07',
    contentLength: undefined,
    timestamp: '2025-11-20T18:50:38.439Z'
  },
  level: 'http',
  timestamp: '2025-11-21 00:20:38:2038'
}
{
  message: {
    method: 'GET',
    url: '/api/v1/admin/orders/68c7d03d12ed1fef2a6cb1b2/payment',
    statusCode: 304,
    responseTime: '78ms',
    ip: '::1',
    userAgent: 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
    userId: '6899f59a7403b47ea1df6b07',
    contentLength: undefined,
    timestamp: '2025-11-20T18:50:43.429Z'
  },
  level: 'http',
  timestamp: '2025-11-21 00:20:43:2043'
}
{
  message: {
    method: 'GET',
    url: '/api/v1/admin/orders/68c7d03d12ed1fef2a6cb1b2/payment',
    statusCode: 304,
    responseTime: '90ms',
    ip: '::1',
    userAgent: 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
    userId: '6899f59a7403b47ea1df6b07',
    contentLength: undefined,
    timestamp: '2025-11-20T18:50:48.442Z'
  },
  level: 'http',
  timestamp: '2025-11-21 00:20:48:2048'
}
{
  message: {
    method: 'GET',
    url: '/api/v1/admin/orders/68c7d03d12ed1fef2a6cb1b2/payment',
    statusCode: 304,
    responseTime: '75ms',
    ip: '::1',
    userAgent: 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
    userId: '6899f59a7403b47ea1df6b07',
    contentLength: undefined,
    timestamp: '2025-11-20T18:50:53.424Z'
  },
  level: 'http',
  timestamp: '2025-11-21 00:20:53:2053'
}
{
  message: {
    method: 'GET',
    url: '/api/v1/admin/orders/68c7d03d12ed1fef2a6cb1b2/payment',
    statusCode: 304,
    responseTime: '80ms',
    ip: '::1',
    userAgent: 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
    userId: '6899f59a7403b47ea1df6b07',
    contentLength: undefined,
    timestamp: '2025-11-20T18:50:58.431Z'
  },
  level: 'http',
  timestamp: '2025-11-21 00:20:58:2058'
}
{
  message: {
    method: 'GET',
    url: '/api/v1/admin/orders/68c7d03d12ed1fef2a6cb1b2/payment',
    statusCode: 304,
    responseTime: '72ms',
    ip: '::1',
    userAgent: 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
    userId: '6899f59a7403b47ea1df6b07',
    contentLength: undefined,
    timestamp: '2025-11-20T18:51:03.422Z'
  },
  level: 'http',
  timestamp: '2025-11-21 00:21:03:213'
}
{
  message: {
    method: 'GET',
    url: '/api/v1/admin/orders/68c7d03d12ed1fef2a6cb1b2/payment',
    statusCode: 304,
    responseTime: '82ms',
    ip: '::1',
    userAgent: 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
    userId: '6899f59a7403b47ea1df6b07',
    contentLength: undefined,
    timestamp: '2025-11-20T18:51:08.433Z'
  },
  level: 'http',
  timestamp: '2025-11-21 00:21:08:218'
}
{
  message: {
    method: 'GET',
    url: '/api/v1/admin/orders/68c7d03d12ed1fef2a6cb1b2/payment',
    statusCode: 304,
    responseTime: '74ms',
    ip: '::1',
    userAgent: 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
    userId: '6899f59a7403b47ea1df6b07',
    contentLength: undefined,
    timestamp: '2025-11-20T18:51:13.431Z'
  },
  level: 'http',
  timestamp: '2025-11-21 00:21:13:2113'
}
{
  message: {
    method: 'GET',
    url: '/api/v1/admin/orders/68c7d03d12ed1fef2a6cb1b2/payment',
    statusCode: 304,
    responseTime: '77ms',
    ip: '::1',
    userAgent: 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
    userId: '6899f59a7403b47ea1df6b07',
    contentLength: undefined,
    timestamp: '2025-11-20T18:51:18.427Z'
  },
  level: 'http',
  timestamp: '2025-11-21 00:21:18:2118'
}
{
  message: {
    method: 'GET',
    url: '/api/v1/admin/orders/68c7d03d12ed1fef2a6cb1b2/payment',
    statusCode: 304,
    responseTime: '92ms',
    ip: '::1',
    userAgent: 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
    userId: '6899f59a7403b47ea1df6b07',
    contentLength: undefined,
    timestamp: '2025-11-20T18:51:23.459Z'
  },
  level: 'http',
  timestamp: '2025-11-21 00:21:23:2123'
}
{
  message: {
    method: 'GET',
    url: '/api/v1/admin/orders/68c7d03d12ed1fef2a6cb1b2/payment',
    statusCode: 304,
    responseTime: '79ms',
    ip: '::1',
    userAgent: 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
    userId: '6899f59a7403b47ea1df6b07',
    contentLength: undefined,
    timestamp: '2025-11-20T18:51:28.437Z'
  },
  level: 'http',
  timestamp: '2025-11-21 00:21:28:2128'
}
{
  message: {
    method: 'GET',
    url: '/api/v1/admin/orders/68c7d03d12ed1fef2a6cb1b2/payment',
    statusCode: 304,
    responseTime: '188ms',
    ip: '::1',
    userAgent: 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
    userId: '6899f59a7403b47ea1df6b07',
    contentLength: undefined,
    timestamp: '2025-11-20T18:52:23.559Z'
  },
  level: 'http',
  timestamp: '2025-11-21 00:22:23:2223'
}
{
  message: {
    method: 'GET',
    url: '/api/v1/admin/orders/68c7d03d12ed1fef2a6cb1b2/payment',
    statusCode: 304,
    responseTime: '309ms',
    ip: '::1',
    userAgent: 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
    userId: '6899f59a7403b47ea1df6b07',
    contentLength: undefined,
    timestamp: '2025-11-20T18:53:23.723Z'
  },
  level: 'http',
  timestamp: '2025-11-21 00:23:23:2323'
}
{
  message: {
    method: 'GET',
    url: '/api/v1/admin/orders/68c7d03d12ed1fef2a6cb1b2/payment',
    statusCode: 304,
    responseTime: '211ms',
    ip: '::1',
    userAgent: 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
    userId: '6899f59a7403b47ea1df6b07',
    contentLength: undefined,
    timestamp: '2025-11-20T18:54:23.585Z'
  },
  level: 'http',
  timestamp: '2025-11-21 00:24:23:2423'
}
{
  message: {
    method: 'GET',
    url: '/api/v1/admin/orders/68c7d03d12ed1fef2a6cb1b2/payment',
    statusCode: 304,
    responseTime: '233ms',
    ip: '::1',
    userAgent: 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
    userId: '6899f59a7403b47ea1df6b07',
    contentLength: undefined,
    timestamp: '2025-11-20T18:55:23.631Z'
  },
  level: 'http',
  timestamp: '2025-11-21 00:25:23:2523'
}
{
  message: {
    method: 'GET',
    url: '/api/v1/admin/orders/68c7d03d12ed1fef2a6cb1b2/payment',
    statusCode: 304,
    responseTime: '216ms',
    ip: '::1',
    userAgent: 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
    userId: '6899f59a7403b47ea1df6b07',
    contentLength: undefined,
    timestamp: '2025-11-20T18:56:23.608Z'
  },
  level: 'http',
  timestamp: '2025-11-21 00:26:23:2623'
}
{
  message: {
    method: 'GET',
    url: '/api/v1/admin/orders/68c7d03d12ed1fef2a6cb1b2/payment',
    statusCode: 304,
    responseTime: '198ms',
    ip: '::1',
    userAgent: 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
    userId: '6899f59a7403b47ea1df6b07',
    contentLength: undefined,
    timestamp: '2025-11-20T18:57:23.614Z'
  },
  level: 'http',
  timestamp: '2025-11-21 00:27:23:2723'
}
{
  message: {
    method: 'GET',
    url: '/api/v1/admin/orders/68c7d03d12ed1fef2a6cb1b2/payment',
    statusCode: 304,
    responseTime: '200ms',
    ip: '::1',
    userAgent: 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
    userId: '6899f59a7403b47ea1df6b07',
    contentLength: undefined,
    timestamp: '2025-11-20T18:58:23.579Z'
  },
  level: 'http',
  timestamp: '2025-11-21 00:28:23:2823'
}
{
  message: {
    method: 'GET',
    url: '/api/v1/admin/orders/68c7d03d12ed1fef2a6cb1b2/payment',
    statusCode: 304,
    responseTime: '194ms',
    ip: '::1',
    userAgent: 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
    userId: '6899f59a7403b47ea1df6b07',
    contentLength: undefined,
    timestamp: '2025-11-20T18:59:23.596Z'
  },
  level: 'http',
  timestamp: '2025-11-21 00:29:23:2923'
}
{
  message: {
    method: 'GET',
    url: '/api/v1/admin/orders/68c7d03d12ed1fef2a6cb1b2/payment',
    statusCode: 200,
    responseTime: '220ms',
    ip: '::1',
    userAgent: 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
    userId: '6899f59a7403b47ea1df6b07',
    contentLength: undefined,
    timestamp: '2025-11-20T19:00:23.646Z'
  },
  level: 'http',
  timestamp: '2025-11-21 00:30:23:3023'
}
{
  message: {
    method: 'GET',
    url: '/api/v1/admin/orders/68c7d03d12ed1fef2a6cb1b2/payment',
    statusCode: 304,
    responseTime: '218ms',
    ip: '::1',
    userAgent: 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
    userId: '6899f59a7403b47ea1df6b07',
    contentLength: undefined,
    timestamp: '2025-11-20T19:01:23.591Z'
  },
  level: 'http',
  timestamp: '2025-11-21 00:31:23:3123'
}
{
  message: 'Received SIGINT. Starting graceful shutdown...',
  timestamp: '2025-11-21 00:32:16:3216',
  level: 'info',
  service: 'butler-api',
  environment: 'development'
}
{
  message: 'Received SIGINT. Starting graceful shutdown...',
  timestamp: '2025-11-21 00:32:16:3216',
  level: 'info',
  service: 'butler-api',
  environment: 'development'
}
