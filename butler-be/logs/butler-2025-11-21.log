{
  message: '🚀 Butler API server running on port 3001',
  timestamp: '2025-11-21 00:08:04:84',
  level: 'info',
  service: 'butler-api',
  environment: 'development'
}
{
  message: '📊 Environment: development',
  timestamp: '2025-11-21 00:08:04:84',
  level: 'info',
  service: 'butler-api',
  environment: 'development'
}
{
  message: '🔒 Security: Enhanced security measures active',
  timestamp: '2025-11-21 00:08:04:84',
  level: 'info',
  service: 'butler-api',
  environment: 'development'
}
{
  message: '📈 Health check: http://localhost:3001/health',
  timestamp: '2025-11-21 00:08:04:84',
  level: 'info',
  service: 'butler-api',
  environment: 'development'
}
{
  message: '📝 Logging: <PERSON> logger initialized',
  timestamp: '2025-11-21 00:08:04:84',
  level: 'info',
  service: 'butler-api',
  environment: 'development'
}
{
  message: 'Initializing database optimizations...',
  timestamp: '2025-11-21 00:08:04:84',
  level: 'info',
  service: 'butler-api',
  environment: 'development'
}
{
  message: 'Database connection optimized',
  timestamp: '2025-11-21 00:08:04:84',
  level: 'info',
  service: 'butler-api',
  environment: 'development'
}
{
  message: 'Starting database index creation...',
  timestamp: '2025-11-21 00:08:04:84',
  level: 'info',
  service: 'butler-api',
  environment: 'development'
}
{
  message: "Cannot read properties of undefined (reading 'collection')",
  stack: "TypeError: Cannot read properties of undefined (reading 'collection')\n" +
    '    at createDatabaseIndexes (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:115:31)\n' +
    '    at initializeDatabaseOptimizations (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:359:11)\n' +
    '    at Server.<anonymous> (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/index.js:251:11)\n' +
    '    at Object.onceWrapper (node:events:632:28)\n' +
    '    at Server.emit (node:events:530:35)\n' +
    '    at emitListeningNT (node:net:1983:10)\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:89:21)',
  context: 'index_creation',
  collection: 'users',
  timestamp: '2025-11-21 00:08:04:84',
  level: 'error',
  service: 'butler-api',
  environment: 'development'
}
{
  message: "Cannot read properties of undefined (reading 'collection')",
  stack: "TypeError: Cannot read properties of undefined (reading 'collection')\n" +
    '    at createDatabaseIndexes (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:115:31)\n' +
    '    at initializeDatabaseOptimizations (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:359:11)\n' +
    '    at Server.<anonymous> (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/index.js:251:11)\n' +
    '    at Object.onceWrapper (node:events:632:28)\n' +
    '    at Server.emit (node:events:530:35)\n' +
    '    at emitListeningNT (node:net:1983:10)\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:89:21)',
  context: 'index_creation',
  collection: 'orders',
  timestamp: '2025-11-21 00:08:04:84',
  level: 'error',
  service: 'butler-api',
  environment: 'development'
}
{
  message: "Cannot read properties of undefined (reading 'collection')",
  stack: "TypeError: Cannot read properties of undefined (reading 'collection')\n" +
    '    at createDatabaseIndexes (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:115:31)\n' +
    '    at initializeDatabaseOptimizations (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:359:11)\n' +
    '    at Server.<anonymous> (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/index.js:251:11)\n' +
    '    at Object.onceWrapper (node:events:632:28)\n' +
    '    at Server.emit (node:events:530:35)\n' +
    '    at emitListeningNT (node:net:1983:10)\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:89:21)',
  context: 'index_creation',
  collection: 'dishes',
  timestamp: '2025-11-21 00:08:04:84',
  level: 'error',
  service: 'butler-api',
  environment: 'development'
}
{
  message: "Cannot read properties of undefined (reading 'collection')",
  stack: "TypeError: Cannot read properties of undefined (reading 'collection')\n" +
    '    at createDatabaseIndexes (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:115:31)\n' +
    '    at initializeDatabaseOptimizations (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:359:11)\n' +
    '    at Server.<anonymous> (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/index.js:251:11)\n' +
    '    at Object.onceWrapper (node:events:632:28)\n' +
    '    at Server.emit (node:events:530:35)\n' +
    '    at emitListeningNT (node:net:1983:10)\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:89:21)',
  context: 'index_creation',
  collection: 'outlets',
  timestamp: '2025-11-21 00:08:04:84',
  level: 'error',
  service: 'butler-api',
  environment: 'development'
}
{
  message: "Cannot read properties of undefined (reading 'collection')",
  stack: "TypeError: Cannot read properties of undefined (reading 'collection')\n" +
    '    at createDatabaseIndexes (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:115:31)\n' +
    '    at initializeDatabaseOptimizations (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:359:11)\n' +
    '    at Server.<anonymous> (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/index.js:251:11)\n' +
    '    at Object.onceWrapper (node:events:632:28)\n' +
    '    at Server.emit (node:events:530:35)\n' +
    '    at emitListeningNT (node:net:1983:10)\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:89:21)',
  context: 'index_creation',
  collection: 'carts',
  timestamp: '2025-11-21 00:08:04:84',
  level: 'error',
  service: 'butler-api',
  environment: 'development'
}
{
  message: "Cannot read properties of undefined (reading 'collection')",
  stack: "TypeError: Cannot read properties of undefined (reading 'collection')\n" +
    '    at createDatabaseIndexes (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:115:31)\n' +
    '    at initializeDatabaseOptimizations (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:359:11)\n' +
    '    at Server.<anonymous> (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/index.js:251:11)\n' +
    '    at Object.onceWrapper (node:events:632:28)\n' +
    '    at Server.emit (node:events:530:35)\n' +
    '    at emitListeningNT (node:net:1983:10)\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:89:21)',
  context: 'index_creation',
  collection: 'payments',
  timestamp: '2025-11-21 00:08:04:84',
  level: 'error',
  service: 'butler-api',
  environment: 'development'
}
{
  message: "Cannot read properties of undefined (reading 'collection')",
  stack: "TypeError: Cannot read properties of undefined (reading 'collection')\n" +
    '    at createDatabaseIndexes (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:115:31)\n' +
    '    at initializeDatabaseOptimizations (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:359:11)\n' +
    '    at Server.<anonymous> (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/index.js:251:11)\n' +
    '    at Object.onceWrapper (node:events:632:28)\n' +
    '    at Server.emit (node:events:530:35)\n' +
    '    at emitListeningNT (node:net:1983:10)\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:89:21)',
  context: 'index_creation',
  collection: 'notifications',
  timestamp: '2025-11-21 00:08:04:84',
  level: 'error',
  service: 'butler-api',
  environment: 'development'
}
{
  message: "Cannot read properties of undefined (reading 'collection')",
  stack: "TypeError: Cannot read properties of undefined (reading 'collection')\n" +
    '    at createDatabaseIndexes (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:115:31)\n' +
    '    at initializeDatabaseOptimizations (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:359:11)\n' +
    '    at Server.<anonymous> (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/index.js:251:11)\n' +
    '    at Object.onceWrapper (node:events:632:28)\n' +
    '    at Server.emit (node:events:530:35)\n' +
    '    at emitListeningNT (node:net:1983:10)\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:89:21)',
  context: 'index_creation',
  collection: 'subscriptions',
  timestamp: '2025-11-21 00:08:04:84',
  level: 'error',
  service: 'butler-api',
  environment: 'development'
}
{
  message: "Cannot read properties of undefined (reading 'collection')",
  stack: "TypeError: Cannot read properties of undefined (reading 'collection')\n" +
    '    at createDatabaseIndexes (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:115:31)\n' +
    '    at initializeDatabaseOptimizations (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:359:11)\n' +
    '    at Server.<anonymous> (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/index.js:251:11)\n' +
    '    at Object.onceWrapper (node:events:632:28)\n' +
    '    at Server.emit (node:events:530:35)\n' +
    '    at emitListeningNT (node:net:1983:10)\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:89:21)',
  context: 'index_creation',
  collection: 'customerfeedbacks',
  timestamp: '2025-11-21 00:08:04:84',
  level: 'error',
  service: 'butler-api',
  environment: 'development'
}
{
  message: "Cannot read properties of undefined (reading 'collection')",
  stack: "TypeError: Cannot read properties of undefined (reading 'collection')\n" +
    '    at createDatabaseIndexes (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:115:31)\n' +
    '    at initializeDatabaseOptimizations (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:359:11)\n' +
    '    at Server.<anonymous> (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/index.js:251:11)\n' +
    '    at Object.onceWrapper (node:events:632:28)\n' +
    '    at Server.emit (node:events:530:35)\n' +
    '    at emitListeningNT (node:net:1983:10)\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:89:21)',
  context: 'index_creation',
  collection: 'conversations',
  timestamp: '2025-11-21 00:08:04:84',
  level: 'error',
  service: 'butler-api',
  environment: 'development'
}
{
  message: "Cannot read properties of undefined (reading 'collection')",
  stack: "TypeError: Cannot read properties of undefined (reading 'collection')\n" +
    '    at createDatabaseIndexes (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:115:31)\n' +
    '    at initializeDatabaseOptimizations (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:359:11)\n' +
    '    at Server.<anonymous> (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/index.js:251:11)\n' +
    '    at Object.onceWrapper (node:events:632:28)\n' +
    '    at Server.emit (node:events:530:35)\n' +
    '    at emitListeningNT (node:net:1983:10)\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:89:21)',
  context: 'index_creation',
  collection: 'auditlogs',
  timestamp: '2025-11-21 00:08:04:84',
  level: 'error',
  service: 'butler-api',
  environment: 'development'
}
{
  message: 'Database index creation completed',
  timestamp: '2025-11-21 00:08:04:84',
  level: 'info',
  service: 'butler-api',
  environment: 'development'
}
{
  message: 'Database optimizations initialized successfully',
  timestamp: '2025-11-21 00:08:04:84',
  level: 'info',
  service: 'butler-api',
  environment: 'development'
}
{
  message: 'Redis not configured, using in-memory cache fallback',
  timestamp: '2025-11-21 00:08:04:84',
  level: 'info',
  service: 'butler-api',
  environment: 'development'
}
{
  message: '⚡ Performance optimizations initialized',
  timestamp: '2025-11-21 00:08:04:84',
  level: 'info',
  service: 'butler-api',
  environment: 'development'
}
{
  message: '⏰ Cron jobs initialized',
  timestamp: '2025-11-21 00:08:04:84',
  level: 'info',
  service: 'butler-api',
  environment: 'development'
}
{
  message: "Can't find /api/v1/user/test-order-validation on this server!",
  stack: undefined,
  url: '/api/v1/user/test-order-validation',
  method: 'POST',
  ip: '::1',
  userAgent: 'curl/8.7.1',
  userId: 'anonymous',
  timestamp: '2025-11-21 00:10:30:1030',
  level: 'error',
  service: 'butler-api',
  environment: 'development'
}
{
  message: '🚀 Butler API server running on port 3001',
  timestamp: '2025-11-21 00:10:45:1045',
  level: 'info',
  service: 'butler-api',
  environment: 'development'
}
{
  message: '📊 Environment: development',
  timestamp: '2025-11-21 00:10:45:1045',
  level: 'info',
  service: 'butler-api',
  environment: 'development'
}
{
  message: '🔒 Security: Enhanced security measures active',
  timestamp: '2025-11-21 00:10:45:1045',
  level: 'info',
  service: 'butler-api',
  environment: 'development'
}
{
  message: '📈 Health check: http://localhost:3001/health',
  timestamp: '2025-11-21 00:10:45:1045',
  level: 'info',
  service: 'butler-api',
  environment: 'development'
}
{
  message: '📝 Logging: Winston logger initialized',
  timestamp: '2025-11-21 00:10:45:1045',
  level: 'info',
  service: 'butler-api',
  environment: 'development'
}
{
  message: 'Initializing database optimizations...',
  timestamp: '2025-11-21 00:10:45:1045',
  level: 'info',
  service: 'butler-api',
  environment: 'development'
}
{
  message: 'Database connection optimized',
  timestamp: '2025-11-21 00:10:45:1045',
  level: 'info',
  service: 'butler-api',
  environment: 'development'
}
{
  message: 'Starting database index creation...',
  timestamp: '2025-11-21 00:10:45:1045',
  level: 'info',
  service: 'butler-api',
  environment: 'development'
}
{
  message: "Cannot read properties of undefined (reading 'collection')",
  stack: "TypeError: Cannot read properties of undefined (reading 'collection')\n" +
    '    at createDatabaseIndexes (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:115:31)\n' +
    '    at initializeDatabaseOptimizations (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:359:11)\n' +
    '    at Server.<anonymous> (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/index.js:251:11)\n' +
    '    at Object.onceWrapper (node:events:632:28)\n' +
    '    at Server.emit (node:events:530:35)\n' +
    '    at emitListeningNT (node:net:1983:10)\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:89:21)',
  context: 'index_creation',
  collection: 'users',
  timestamp: '2025-11-21 00:10:45:1045',
  level: 'error',
  service: 'butler-api',
  environment: 'development'
}
{
  message: "Cannot read properties of undefined (reading 'collection')",
  stack: "TypeError: Cannot read properties of undefined (reading 'collection')\n" +
    '    at createDatabaseIndexes (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:115:31)\n' +
    '    at initializeDatabaseOptimizations (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:359:11)\n' +
    '    at Server.<anonymous> (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/index.js:251:11)\n' +
    '    at Object.onceWrapper (node:events:632:28)\n' +
    '    at Server.emit (node:events:530:35)\n' +
    '    at emitListeningNT (node:net:1983:10)\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:89:21)',
  context: 'index_creation',
  collection: 'orders',
  timestamp: '2025-11-21 00:10:45:1045',
  level: 'error',
  service: 'butler-api',
  environment: 'development'
}
{
  message: "Cannot read properties of undefined (reading 'collection')",
  stack: "TypeError: Cannot read properties of undefined (reading 'collection')\n" +
    '    at createDatabaseIndexes (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:115:31)\n' +
    '    at initializeDatabaseOptimizations (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:359:11)\n' +
    '    at Server.<anonymous> (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/index.js:251:11)\n' +
    '    at Object.onceWrapper (node:events:632:28)\n' +
    '    at Server.emit (node:events:530:35)\n' +
    '    at emitListeningNT (node:net:1983:10)\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:89:21)',
  context: 'index_creation',
  collection: 'dishes',
  timestamp: '2025-11-21 00:10:45:1045',
  level: 'error',
  service: 'butler-api',
  environment: 'development'
}
{
  message: "Cannot read properties of undefined (reading 'collection')",
  stack: "TypeError: Cannot read properties of undefined (reading 'collection')\n" +
    '    at createDatabaseIndexes (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:115:31)\n' +
    '    at initializeDatabaseOptimizations (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:359:11)\n' +
    '    at Server.<anonymous> (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/index.js:251:11)\n' +
    '    at Object.onceWrapper (node:events:632:28)\n' +
    '    at Server.emit (node:events:530:35)\n' +
    '    at emitListeningNT (node:net:1983:10)\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:89:21)',
  context: 'index_creation',
  collection: 'outlets',
  timestamp: '2025-11-21 00:10:45:1045',
  level: 'error',
  service: 'butler-api',
  environment: 'development'
}
{
  message: "Cannot read properties of undefined (reading 'collection')",
  stack: "TypeError: Cannot read properties of undefined (reading 'collection')\n" +
    '    at createDatabaseIndexes (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:115:31)\n' +
    '    at initializeDatabaseOptimizations (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:359:11)\n' +
    '    at Server.<anonymous> (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/index.js:251:11)\n' +
    '    at Object.onceWrapper (node:events:632:28)\n' +
    '    at Server.emit (node:events:530:35)\n' +
    '    at emitListeningNT (node:net:1983:10)\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:89:21)',
  context: 'index_creation',
  collection: 'carts',
  timestamp: '2025-11-21 00:10:45:1045',
  level: 'error',
  service: 'butler-api',
  environment: 'development'
}
{
  message: "Cannot read properties of undefined (reading 'collection')",
  stack: "TypeError: Cannot read properties of undefined (reading 'collection')\n" +
    '    at createDatabaseIndexes (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:115:31)\n' +
    '    at initializeDatabaseOptimizations (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:359:11)\n' +
    '    at Server.<anonymous> (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/index.js:251:11)\n' +
    '    at Object.onceWrapper (node:events:632:28)\n' +
    '    at Server.emit (node:events:530:35)\n' +
    '    at emitListeningNT (node:net:1983:10)\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:89:21)',
  context: 'index_creation',
  collection: 'payments',
  timestamp: '2025-11-21 00:10:45:1045',
  level: 'error',
  service: 'butler-api',
  environment: 'development'
}
{
  message: "Cannot read properties of undefined (reading 'collection')",
  stack: "TypeError: Cannot read properties of undefined (reading 'collection')\n" +
    '    at createDatabaseIndexes (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:115:31)\n' +
    '    at initializeDatabaseOptimizations (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:359:11)\n' +
    '    at Server.<anonymous> (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/index.js:251:11)\n' +
    '    at Object.onceWrapper (node:events:632:28)\n' +
    '    at Server.emit (node:events:530:35)\n' +
    '    at emitListeningNT (node:net:1983:10)\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:89:21)',
  context: 'index_creation',
  collection: 'notifications',
  timestamp: '2025-11-21 00:10:45:1045',
  level: 'error',
  service: 'butler-api',
  environment: 'development'
}
{
  message: "Cannot read properties of undefined (reading 'collection')",
  stack: "TypeError: Cannot read properties of undefined (reading 'collection')\n" +
    '    at createDatabaseIndexes (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:115:31)\n' +
    '    at initializeDatabaseOptimizations (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:359:11)\n' +
    '    at Server.<anonymous> (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/index.js:251:11)\n' +
    '    at Object.onceWrapper (node:events:632:28)\n' +
    '    at Server.emit (node:events:530:35)\n' +
    '    at emitListeningNT (node:net:1983:10)\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:89:21)',
  context: 'index_creation',
  collection: 'subscriptions',
  timestamp: '2025-11-21 00:10:45:1045',
  level: 'error',
  service: 'butler-api',
  environment: 'development'
}
{
  message: "Cannot read properties of undefined (reading 'collection')",
  stack: "TypeError: Cannot read properties of undefined (reading 'collection')\n" +
    '    at createDatabaseIndexes (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:115:31)\n' +
    '    at initializeDatabaseOptimizations (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:359:11)\n' +
    '    at Server.<anonymous> (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/index.js:251:11)\n' +
    '    at Object.onceWrapper (node:events:632:28)\n' +
    '    at Server.emit (node:events:530:35)\n' +
    '    at emitListeningNT (node:net:1983:10)\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:89:21)',
  context: 'index_creation',
  collection: 'customerfeedbacks',
  timestamp: '2025-11-21 00:10:45:1045',
  level: 'error',
  service: 'butler-api',
  environment: 'development'
}
{
  message: "Cannot read properties of undefined (reading 'collection')",
  stack: "TypeError: Cannot read properties of undefined (reading 'collection')\n" +
    '    at createDatabaseIndexes (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:115:31)\n' +
    '    at initializeDatabaseOptimizations (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:359:11)\n' +
    '    at Server.<anonymous> (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/index.js:251:11)\n' +
    '    at Object.onceWrapper (node:events:632:28)\n' +
    '    at Server.emit (node:events:530:35)\n' +
    '    at emitListeningNT (node:net:1983:10)\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:89:21)',
  context: 'index_creation',
  collection: 'conversations',
  timestamp: '2025-11-21 00:10:45:1045',
  level: 'error',
  service: 'butler-api',
  environment: 'development'
}
{
  message: "Cannot read properties of undefined (reading 'collection')",
  stack: "TypeError: Cannot read properties of undefined (reading 'collection')\n" +
    '    at createDatabaseIndexes (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:115:31)\n' +
    '    at initializeDatabaseOptimizations (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/utils/database-optimization.js:359:11)\n' +
    '    at Server.<anonymous> (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/index.js:251:11)\n' +
    '    at Object.onceWrapper (node:events:632:28)\n' +
    '    at Server.emit (node:events:530:35)\n' +
    '    at emitListeningNT (node:net:1983:10)\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:89:21)',
  context: 'index_creation',
  collection: 'auditlogs',
  timestamp: '2025-11-21 00:10:45:1045',
  level: 'error',
  service: 'butler-api',
  environment: 'development'
}
{
  message: 'Database index creation completed',
  timestamp: '2025-11-21 00:10:45:1045',
  level: 'info',
  service: 'butler-api',
  environment: 'development'
}
{
  message: 'Database optimizations initialized successfully',
  timestamp: '2025-11-21 00:10:45:1045',
  level: 'info',
  service: 'butler-api',
  environment: 'development'
}
{
  message: 'Redis not configured, using in-memory cache fallback',
  timestamp: '2025-11-21 00:10:45:1045',
  level: 'info',
  service: 'butler-api',
  environment: 'development'
}
{
  message: '⚡ Performance optimizations initialized',
  timestamp: '2025-11-21 00:10:45:1045',
  level: 'info',
  service: 'butler-api',
  environment: 'development'
}
{
  message: '⏰ Cron jobs initialized',
  timestamp: '2025-11-21 00:10:45:1045',
  level: 'info',
  service: 'butler-api',
  environment: 'development'
}
{
  message: 'Performance: cache_miss',
  duration: '0ms',
  key: 'butler:dishes:categoryId:all|foodChainId:6899f5527403b47ea1df69a2|includeUnavailable:true|outletId:all|personalize:false',
  timestamp: '2025-11-21 00:13:32:1332',
  level: 'info',
  service: 'butler-api',
  environment: 'development'
}
{
  message: 'Performance: cache_miss',
  duration: '0ms',
  key: 'butler:dishes:categoryId:all|foodChainId:6899f5527403b47ea1df69a2|includeUnavailable:true|outletId:all|personalize:false',
  timestamp: '2025-11-21 00:13:33:1333',
  level: 'info',
  service: 'butler-api',
  environment: 'development'
}
{
  message: 'Performance: cache_miss',
  duration: '0ms',
  key: 'butler:dishes:categoryId:all|foodChainId:6899f5527403b47ea1df69a2|includeUnavailable:true|outletId:all|personalize:false',
  timestamp: '2025-11-21 00:13:33:1333',
  level: 'info',
  service: 'butler-api',
  environment: 'development'
}
{
  message: 'Slow request detected',
  stack: 'Error: Slow request detected\n' +
    '    at ServerResponse.<anonymous> (file:///Users/<USER>/Desktop/projects/personal/butler/butler-be/middlewares/errorHandler.js:261:18)\n' +
    '    at ServerResponse.emit (node:events:530:35)\n' +
    '    at onFinish (node:_http_outgoing:1082:10)\n' +
    '    at callback (node:internal/streams/writable:766:21)\n' +
    '    at afterWrite (node:internal/streams/writable:710:5)\n' +
    '    at afterWriteTick (node:internal/streams/writable:696:10)\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:89:21)',
  url: '/api/v1/admin/orders/create',
  method: 'POST',
  duration: '2982ms',
  threshold: '2000ms',
  ip: '::1',
  userId: '6899f59a7403b47ea1df6b07',
  timestamp: '2025-11-21 00:13:48:1348',
  level: 'error',
  service: 'butler-api',
  environment: 'development'
}
{
  message: "Can't find /api/v1/user/personalized-menu?foodChainId=6899f5527403b47ea1df69a2&outletId=6899fa5358dbcc8f5ced8116 on this server!",
  stack: undefined,
  url: '/api/v1/user/personalized-menu?foodChainId=6899f5527403b47ea1df69a2&outletId=6899fa5358dbcc8f5ced8116',
  method: 'GET',
  ip: '::1',
  userAgent: 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
  userId: 'anonymous',
  timestamp: '2025-11-21 00:18:40:1840',
  level: 'error',
  service: 'butler-api',
  environment: 'development'
}
{
  message: "Can't find /api/v1/user/personalized-menu?foodChainId=6899f5527403b47ea1df69a2&outletId=6899fa5358dbcc8f5ced8116 on this server!",
  stack: undefined,
  url: '/api/v1/user/personalized-menu?foodChainId=6899f5527403b47ea1df69a2&outletId=6899fa5358dbcc8f5ced8116',
  method: 'GET',
  ip: '::1',
  userAgent: 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
  userId: 'anonymous',
  timestamp: '2025-11-21 00:18:40:1840',
  level: 'error',
  service: 'butler-api',
  environment: 'development'
}
{
  message: 'Performance: cache_miss',
  duration: '0ms',
  key: 'butler:dishes:categoryId:all|foodChainId:6899f5527403b47ea1df69a2|includeUnavailable:false|outletId:6899fa5358dbcc8f5ced8116|personalize:true',
  timestamp: '2025-11-21 00:18:40:1840',
  level: 'info',
  service: 'butler-api',
  environment: 'development'
}
{
  message: 'Performance: cache_set',
  duration: '1ms',
  key: 'butler:dishes:categoryId:all|foodChainId:6899f5527403b47ea1df69a2|includeUnavailable:false|outletId:6899fa5358dbcc8f5ced8116|personalize:true',
  ttl: 300,
  timestamp: '2025-11-21 00:18:40:1840',
  level: 'info',
  service: 'butler-api',
  environment: 'development'
}
{
  message: 'Performance: getDishes_database_query',
  duration: '109ms',
  dishCount: 40,
  personalized: true,
  timestamp: '2025-11-21 00:18:40:1840',
  level: 'info',
  service: 'butler-api',
  environment: 'development'
}
{
  message: 'Performance: cache_hit',
  duration: '0ms',
  key: 'butler:dishes:categoryId:all|foodChainId:6899f5527403b47ea1df69a2|includeUnavailable:false|outletId:6899fa5358dbcc8f5ced8116|personalize:true',
  timestamp: '2025-11-21 00:18:40:1840',
  level: 'info',
  service: 'butler-api',
  environment: 'development'
}
{
  message: 'Performance: getDishes_cache_hit',
  duration: '0ms',
  cacheKey: 'butler:dishes:categoryId:all|foodChainId:6899f5527403b47ea1df69a2|includeUnavailable:false|outletId:6899fa5358dbcc8f5ced8116|personalize:true',
  dishCount: 40,
  timestamp: '2025-11-21 00:18:40:1840',
  level: 'info',
  service: 'butler-api',
  environment: 'development'
}
{
  message: 'Received SIGINT. Starting graceful shutdown...',
  timestamp: '2025-11-21 00:32:16:3216',
  level: 'info',
  service: 'butler-api',
  environment: 'development'
}
{
  message: 'Received SIGINT. Starting graceful shutdown...',
  timestamp: '2025-11-21 00:32:16:3216',
  level: 'info',
  service: 'butler-api',
  environment: 'development'
}
