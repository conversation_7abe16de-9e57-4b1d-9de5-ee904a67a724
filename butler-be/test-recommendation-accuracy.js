/**
 * Test Script for Recommendation Accuracy
 * Tests the improved recommendation engine with various edge cases
 */

import { getOptimizedRecommendations } from './services/recommendation-orchestrator.js';
import { getRecommendationMetrics } from './services/recommendation-monitor.js';

// Test cases that were previously problematic
const testCases = [
  {
    query: "I want momos",
    expectedDishTypes: ["momos", "dumpling"],
    description: "Specific momos request"
  },
  {
    query: "Show me noodles",
    expectedDishTypes: ["noodles", "pasta"],
    description: "Specific noodles request"
  },
  {
    query: "I want momos and noodles",
    expectedDishTypes: ["momos", "noodles"],
    description: "Multiple specific dishes"
  },
  {
    query: "Vegetarian food please",
    expectedVeg: true,
    description: "Vegetarian preference"
  },
  {
    query: "Non veg options",
    expectedVeg: false,
    description: "Non-vegetarian preference"
  },
  {
    query: "Something spicy",
    expectedGeneral: true,
    description: "General preference"
  }
];

// Mock available dishes for testing
const mockDishes = [
  {
    _id: "1",
    name: "Chicken Momos",
    description: "Steamed chicken dumplings",
    category: "appetizer",
    tags: ["momos", "dumpling", "steamed"],
    isVeg: false,
    cuisine: "Tibetan"
  },
  {
    _id: "2",
    name: "Veg Momos",
    description: "Steamed vegetable dumplings",
    category: "appetizer",
    tags: ["momos", "dumpling", "steamed"],
    isVeg: true,
    cuisine: "Tibetan"
  },
  {
    _id: "3",
    name: "Chicken Noodles",
    description: "Stir-fried noodles with chicken",
    category: "main",
    tags: ["noodles", "stir-fry"],
    isVeg: false,
    cuisine: "Chinese"
  },
  {
    _id: "4",
    name: "Veg Noodles",
    description: "Stir-fried vegetable noodles",
    category: "main",
    tags: ["noodles", "stir-fry"],
    isVeg: true,
    cuisine: "Chinese"
  },
  {
    _id: "5",
    name: "Paneer Curry",
    description: "Spicy paneer in rich gravy",
    category: "main",
    tags: ["curry", "spicy", "paneer"],
    isVeg: true,
    cuisine: "Indian"
  }
];

/**
 * Run recommendation accuracy tests
 */
async function runAccuracyTests() {
  console.log("🧪 Starting Recommendation Accuracy Tests...\n");
  
  let passedTests = 0;
  let totalTests = testCases.length;
  
  for (const testCase of testCases) {
    console.log(`\n📝 Testing: ${testCase.description}`);
    console.log(`Query: "${testCase.query}"`);
    
    try {
      const result = await getOptimizedRecommendations(
        testCase.query,
        mockDishes,
        { userId: "test-user" }
      );
      
      const passed = validateTestResult(result, testCase);
      
      if (passed) {
        console.log("✅ Test PASSED");
        passedTests++;
      } else {
        console.log("❌ Test FAILED");
      }
      
      // Log recommendation details
      console.log(`Recommendations: ${result.recommendations.map(d => d.name).join(", ")}`);
      console.log(`AI Message: ${result.aiResponse.aiMessage}`);
      
    } catch (error) {
      console.log("❌ Test FAILED with error:", error.message);
    }
  }
  
  console.log(`\n📊 Test Results: ${passedTests}/${totalTests} tests passed`);
  console.log(`Accuracy: ${(passedTests/totalTests*100).toFixed(1)}%`);
  
  // Show monitoring metrics
  const metrics = getRecommendationMetrics();
  console.log("\n📈 Monitoring Metrics:");
  console.log(`Total Requests: ${metrics.totalRequests}`);
  console.log(`Accuracy: ${metrics.accuracyPercentage}%`);
  console.log(`Common Issues:`, metrics.topIssues);
}

/**
 * Validate test result against expected criteria
 */
function validateTestResult(result, testCase) {
  const { recommendations, aiResponse } = result;
  
  if (!recommendations || recommendations.length === 0) {
    console.log("❌ No recommendations returned");
    return false;
  }
  
  // Check specific dish types
  if (testCase.expectedDishTypes) {
    const hasExpectedDishes = testCase.expectedDishTypes.some(expectedType => 
      recommendations.some(dish => 
        dish.name.toLowerCase().includes(expectedType) ||
        dish.tags.some(tag => tag.toLowerCase().includes(expectedType))
      )
    );
    
    if (!hasExpectedDishes) {
      console.log(`❌ Expected dish types ${testCase.expectedDishTypes.join(", ")} not found`);
      return false;
    }
  }
  
  // Check vegetarian preference
  if (testCase.expectedVeg !== undefined) {
    const allMatchPreference = recommendations.every(dish => dish.isVeg === testCase.expectedVeg);
    
    if (!allMatchPreference) {
      console.log(`❌ Vegetarian preference not respected (expected: ${testCase.expectedVeg})`);
      return false;
    }
  }
  
  // Check AI message consistency
  const aiMessage = aiResponse.aiMessage.toLowerCase();
  const recommendedDishNames = recommendations.map(d => d.name.toLowerCase());
  
  // Basic check: AI message should mention at least one recommended dish
  const mentionsRecommendedDish = recommendedDishNames.some(dishName => 
    aiMessage.includes(dishName.split(' ')[0]) // Check first word of dish name
  );
  
  if (!mentionsRecommendedDish && !testCase.expectedGeneral) {
    console.log("❌ AI message doesn't mention any recommended dishes");
    return false;
  }
  
  return true;
}

// Run tests if this file is executed directly
if (import.meta.url === `file://${process.argv[1]}`) {
  runAccuracyTests().catch(console.error);
}

export { runAccuracyTests };
