import { Languages } from "lucide-react";
import React, { useState, useEffect } from "react";
import { SupportedLanguage } from "../hooks/useLanguagePreference";
interface LanguageSelectorProps {
  selectedLanguage: string;
  onLanguageChange: (language: SupportedLanguage) => void;
  className?: string;
}

const languages: {
  code: SupportedLanguage;
  name: string;
  flag: string;
  nativeName: string;
}[] = [
  { code: "en", name: "English", flag: "🇺🇸", nativeName: "EN" },
  { code: "hi", name: "हिन्दी", flag: "🇮🇳", nativeName: "HI" },
];

const LanguageSelector: React.FC<LanguageSelectorProps> = ({
  selectedLanguage,
  onLanguageChange,
  className = "",
}) => {
  const [mounted, setMounted] = useState(false);

  useEffect(() => {
    setMounted(true);
  }, []);

  const selectedLang =
    languages.find((lang) => lang.code === selectedLanguage) || languages[0];

  const handleLanguageToggle = () => {
    // Toggle between English and Hindi
    const newLanguage = selectedLanguage === "en" ? "hi" : "en";
    onLanguageChange(newLanguage);

    // Store preference in localStorage
    if (typeof window !== "undefined") {
      localStorage.setItem("butler-conversation-language", newLanguage);
    }
  };

  // Don't render until mounted to avoid hydration issues
  if (!mounted) {
    return (
      <div
        className={`inline-flex items-center space-x-2 px-3 py-2 ${className}`}
      >
        <Languages className="h-4 w-4 text-gray-500" />
        <span className="text-sm text-gray-600">Loading...</span>
      </div>
    );
  }

  return (
    <button
      type="button"
      className={`inline-flex items-center justify-center rounded-full p-2 border border-gray-300 shadow-sm md:px-3 md:py-2 bg-white font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 transition-colors duration-200 ${className}`}
      onClick={handleLanguageToggle}
      title={`Switch to ${selectedLanguage === "en" ? "Hindi" : "English"}`}
    >
      <span className="text-sm font-medium">{selectedLang.nativeName}</span>
    </button>
  );
};

export default LanguageSelector;
